<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Http;
use App\Models\CountryTimezone;

class CountryTimezoneSeeder extends Seeder
{
    public function run(): void
    {
        $apiKey = '26VJGJCTXCKP';

        $response = Http::get('http://api.timezonedb.com/v2.1/list-time-zone', [
            'key' => $apiKey,
            'format' => 'json',
        ]);

        if (!$response->ok() || $response->json('status') !== 'OK') {
            $this->command->error('❌ Failed to fetch timezones from TimeZoneDB API');
            return;
        }

        $zones = $response->json('zones', []);

        foreach ($zones as $zone) {
            CountryTimezone::updateOrCreate(
                ['country_code' => $zone['countryCode']],
                [
                    'country_name' => $zone['countryName'],
                    'timezone'     => $zone['zoneName'],
                ]
            );
        }

        $this->command->info('✅ Seeded ' . count($zones) . ' country timezones successfully.');
    }
}
