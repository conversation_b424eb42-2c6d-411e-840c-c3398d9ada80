<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedules.
     */
    protected function schedule(Schedule $schedule): void
    {
      $schedule->command('app:schedule-private')->monthly()->at('00:00:01');
      $schedule->command('app:schedule-syncwp')->hourly();
      $schedule->job(new \App\Jobs\SendHomeworkReminderJob)->everyMinute();
      $schedule->job(new \App\Jobs\InvoiceUnpaidReminderJob)->everyMinute();
      $schedule->job(new \App\Jobs\InvoiceUnpaidWeeklyReminderJob)
             ->weeklyOn(1, '15:00');
        $schedule->job(new \App\Jobs\SendUpcomingClassReminder)->everyFiveMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
