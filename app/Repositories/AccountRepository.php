<?php

namespace App\Repositories;
use Prettus\Repository\Eloquent\BaseRepository;

class AccountRepository extends BaseRepository {

  function model()
  {
    return "App\\Models\\Account";
  }

  public function filter($attribute = []){
    $name = $attribute['name'] ?? null;
    $phone = $attribute['phone'] ?? null;
    $level = $attribute['level'] ?? null;
    $class = $attribute['class'] ?? null;
    $email = $attribute['email'] ?? null;
    $class = ($class == 'all') ? null : $class;
    $level = ($level == 'all') ? null : $level;
    $country = $attribute['country'] ?? null;   
    $birthYear = $attribute['birth_year'] ?? null;
    $column = 'created_at';
    $orderBy = 'orderByDesc';
    $arrayFilter = ['no','user_name','name_english','name_korea','created_at','level_sort'];
    foreach ($attribute as $key => $value) {
      if (($value === 'desc' || $value === 'asc') && in_array($key, $arrayFilter)) {
          $column = $key;
        if($value == 'asc'){
          $orderBy = 'orderBy';
        }
        if($key == 'parent'){
          $column = 'parent.name_english';
        }else if($key == 'phone_parent'){
          $column = 'parent.phone';
        }else if($key == 'level_sort'){
          $column = 'level';
        }else if($key == 'no'){
          $column = 'id';
        }else if($key == 'reading'){

        }
        break;
      }
    }
    $name =  convertKeyword($name);
    $users = $this->model->with('classes')
      ->join('accounts as parent', 'accounts.user_id', '=', 'parent.user_id')
      ->where('accounts.type', 'student')
      ->where('parent.type', 'parent')
      ->where('parent.deleted_at', null)
      ->when($name, function ($query) use ($name) {
        $query->where(function ($query) use ($name) {
          $query->where('accounts.name_english', 'LIKE', '%' . $name . '%')
            ->orWhere('accounts.name_korea', 'LIKE', '%' . $name . '%');
        });
      })
      ->when($phone, function ($query) use ($phone) {
        $query->where('parent.phone', 'LIKE', '%' . $phone . '%');
      })
      ->when($level, function ($query) use ($level) {
        $query->where('accounts.level', $level);
      })
      ->when($email, function ($query) use ($email) {
        $query->where('accounts.email', 'LIKE', '%' . $email . '%');
      })
      ->when($class, function ($query) use ($class) {
        $query->whereHas('classes',function ($q) use ($class){
          $q->where('class_id',$class);
        });
      })
      ->when($country, function ($query) use ($country) {
          $query->where('accounts.country_id', $country);
      })
      ->when($birthYear, function ($query) use ($birthYear) {
          $query->whereYear('accounts.birthday', $birthYear);
      })
      ->select('accounts.*', 'parent.name_english as parent_name','parent.phone as parent_phone')
      ->$orderBy($column)
      ->paginate(10)
      ->withQueryString();
    return $users;
  }

  public function filterParent($attribute = []){
    $name = $attribute['name'] ?? null;
    $date = $attribute['date'] ?? null;
    $status = $attribute['status'] ?? null;
    $column = 'created_at';
    $orderBy = 'orderByDesc';
    $arrayFilter = ['no','name_english','email','student_id','created_at','status_sort'];
    foreach ($attribute as $key => $value) {
      if (($value === 'desc' || $value === 'asc') && in_array($key, $arrayFilter)) {
        $column = $key;
        if($value == 'desc'){
          $orderBy = 'orderByDesc';
        }
        if($value == 'asc'){
          $orderBy = 'orderBy';
        }
        if($key == 'student_id'){
          $column = 'student.user_name';
        }else if($key == 'created_at'){
          $column = 'student.created_at';
        }else if($key == 'status_sort'){
          $column = 'student.status';
        }else if($key == 'no'){
          $column = 'name_english';
        }
        break;
      }
    }
    $name =  convertKeyword($name);
    $parents = $this->model
      ->join('accounts as student', 'accounts.user_id', '=', 'student.user_id')
      ->when($name, function ($query) use ($name) {
        $query->where('accounts.name_english', 'LIKE', '%' . $name . '%');
      })
      ->when($date, function ($query) use ($date) {
        $query->whereDate('student.created_at',$date);
      })
      ->when($status, function ($query) use ($status) {
        $query->where('accounts.status',$status);
      })
      ->where('accounts.type', 'parent')
      ->where('student.type', 'student')
      ->where('student.deleted_at', null)
      ->select('accounts.*', 'student.user_name as student_id','student.status as status_student','student.created_at as student_created_at','student.id as ids')
      ->$orderBy($column)
      ->paginate(10)
      ->withQueryString();
    return $parents;
  }

}
