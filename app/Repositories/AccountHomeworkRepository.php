<?php
namespace App\Repositories;

use Prettus\Repository\Eloquent\BaseRepository;

class AccountHomeworkRepository extends BaseRepository
{
    function model()
    {
        return 'App\\Models\\AccountHomework';
    }

    /**
     * Retrieve first data of repository, or create new Entity
     *
     * @param array $attribute
     * @param array $values
     *
     * @return mixed
     */
    public function firstOrCreate(array $attributes = [], array $values = [])
    {
        $this->applyCriteria();
        $this->applyScope();

        $temporarySkipPresenter = $this->skipPresenter;
        $this->skipPresenter(true);

        $model = $this->model->firstOrCreate($attributes, $values);
        $this->skipPresenter($temporarySkipPresenter);

        $this->resetModel();

        return $this->parserResult($model);
    }

    public function updateStartTimeOnce($accountId, $homeworkId, $mainHomeworkId, array $data)
    {
        $updated = $this->model
            ->where('account_id', $accountId)
            ->where('homework_id', $homeworkId)
            ->where('main_homework_id', $mainHomeworkId)
            ->whereNull('start_time')
            ->limit(1)
            ->update($data);
        if ($updated) {
            $doingHomeworks = session('doing_homework_ids', []);
            if (!in_array($homeworkId, $doingHomeworks)) {
                $doingHomeworks[] = $homeworkId;
            }
            session(['doing_homework_ids' => $doingHomeworks]);
        }
    }

    public function updateByAccountAndHomework($accountId, $homeworkId, $mainHomeworkId, array $data)
    {
        return $this->model
            ->where('account_id', $accountId)
            ->where('homework_id', $homeworkId)
            ->where('main_homework_id', $mainHomeworkId)
            ->update($data);
    }

    public function getAccountHomeworkByAccountAndHomework($accountId, $homeworkId, $mainHomeworkId)
    {
        return $this->model
            ->where('account_id', $accountId)
            ->where('homework_id', $homeworkId)
            ->where('main_homework_id', $mainHomeworkId)
            ->whereNotNull('start_time')
            ->whereNotNull('end_time')
            ->first();
    }
}
