<?php

namespace App\Repositories;
use Carbon\Carbon;
use Prettus\Repository\Eloquent\BaseRepository;

class ClassScheduleRepository extends BaseRepository {

  function model()
  {
    return "App\\Models\\ClassSchedule";
  }

  public function getSesion($schedules){
    $session = 1;
    $current_week_monday = null;
    $start_class = 0;
    $current_class = null;
    foreach ($schedules as $key => &$value) {
        if(!empty($value->session)){
            $session = $value->session;
            $value['session'] = $session;
            continue;
        }
      $sunday = null;
      $sundayOption = null;
      $breakStart  = null;
      $breakOptionStart  = null;
      if(!empty($value->classes->terms)){
        $breakStart = $value->classes->terms->break_start;
        $sunday = date("Y-m-d", strtotime("sunday this week", strtotime($value->classes->terms->break_end)));
        if($value->classes->terms->break_option_start){
          $breakOptionStart = $value->classes->terms->break_option_start;
          $sundayOption = date("Y-m-d", strtotime("sunday this week", strtotime($value->classes->terms->break_option_end)));
        }
      }
      if($current_class && $current_class != $value->class_id){
        $start_class = 0;
      }else{
        $start_class++;
      }
      if($key == 0 || $start_class == 0){
        $current_class = $value->class_id;
        $session = 1;
        $current_week_monday = date('Y-m-d', strtotime('monday this week',strtotime($value['date'])));
      }
      if($breakStart){
        if (date('Y-m-d', strtotime('monday this week', strtotime($value['date']))) !== $current_week_monday
          && !($breakStart < $value['date'] && $sunday > $value['date'])) {
          if($breakOptionStart){
            if(!($breakOptionStart <= $value['date'] && $sundayOption >= $value['date'])){
              $session++;
              $current_week_monday = date('Y-m-d', strtotime('monday this week', strtotime($value['date'])));
            }
          }else{
            $session++;
            $current_week_monday = date('Y-m-d', strtotime('monday this week', strtotime($value['date'])));
          }
        }
        $value['session'] = $session;
      }else{
        $value['session'] = null;
      }
    }

    return $schedules;
  }

}
