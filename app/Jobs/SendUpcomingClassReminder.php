<?php

namespace App\Jobs;

use App\Mail\UpcomingClassMail;
use App\Models\ClassSchedule;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendUpcomingClassReminder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(): void
    {
        $now = Carbon::now();
        $oneHourLater = $now->copy()->addHour();

        $schedules = ClassSchedule::with(['classes.students.country', 'classes.students.parent'])
            ->where('date', $now->toDateString())
            ->whereNull('first_reminder_sent_mail_at')
            ->get();

        $filtered = $schedules->filter(function ($schedule) use ($now, $oneHourLater) {
            $startTime = Carbon::createFromFormat('H:i', $schedule->start);
            return $startTime->between($now, $oneHourLater);
        });

        if ($filtered->isEmpty()) {
            return;
        }

        $sentScheduleIds = [];

        foreach ($filtered as $schedule) {
            $class = $schedule->classes;

            if (!$class || $class->status !== 'active') {
                continue;
            }

            $students = $class->students;
            if ($students->isEmpty()) {
                continue;
            }

            $emails = collect();
            foreach ($students as $student) {
                $country = $student->country; 
                $timezone = $country?->timezone ?? config('app.timezone');
                $countryCode = $country?->country_code ?? '';
                $countryName = $country?->country_name ?? '';
                $startDateTime = Carbon::createFromFormat(
                    'Y-m-d H:i',
                    $schedule->date . ' ' . $schedule->start,
                    config('app.timezone') 
                )->setTimezone($timezone); 

                $formattedTime = trim(
                    $countryCode . ': ' . $startDateTime->format('g:i A') . ' ' . $countryName . ' time.'
                );

                if (!empty($student->email)) {
                    $emails->push([
                        'email' => $student->email,
                        'formattedTime' => $formattedTime
                    ]);
                }

                if ($student->parent && !empty($student->parent->email) && empty($student->email)) {
                    $emails->push([
                        'email' => $student->parent->email,
                        'formattedTime' => $formattedTime
                    ]);
                }
            }
            foreach ($emails->unique('email') as $item) {

                Mail::to($item['email'])->queue(new UpcomingClassMail(
                    $class->name,
                    $schedule->date,
                    $item['formattedTime'],
                    $class->link,
                    $student->name_english
                ));
            }

            $sentScheduleIds[] = $schedule->id;
        }

        // Update cột reminder
        if (!empty($sentScheduleIds)) {
            ClassSchedule::whereIn('id', $sentScheduleIds)
                ->update(['first_reminder_sent_mail_at' => Carbon::now()]);
        }
    }
}
