<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateInvoiceRequest;
use App\Jobs\ProcessInvoiceJob;
use App\Models\Account;
use App\Models\Classes;
use App\Models\Invoice;
use App\Models\InvoiceStudent;
use App\Models\ProductInvoice;
use App\Repositories\AccountRepository;
use App\Repositories\InvoiceRepository;
use App\Repositories\ProductInvoiceRepository;
use App\Services\InvoiceService;
use Illuminate\Http\Request;
use GuzzleHttp;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoiceController extends Controller
{
    public $invoiceRepository;
    public $productInvoiceRepository;
    protected $accountRepository;
    public function __construct(InvoiceRepository $invoiceRepository, AccountRepository $accountRepository,
                                ProductInvoiceRepository $productInvoiceRepository,
                                private InvoiceService $invoiceService
    ){
      $this->invoiceRepository = $invoiceRepository;
      $this->productInvoiceRepository = $productInvoiceRepository;
      $this->accountRepository = $accountRepository;
    }
    public function index(Request $request){
      $invoices = $this->invoiceRepository->filter($request->all(), 'invoice');
      if($request->id == 'desc'){
        $totalItems = $invoices->total();
        $index = $totalItems - ($invoices->currentPage() - 1) * $invoices->perPage();
      }else{
        $index = ($invoices->currentPage() - 1) * $invoices->perPage() + 1;
      }
      return view('admin.invoice.index',compact('invoices','index'));
    }

     public function refunds(Request $request){
          $invoices = $this->invoiceRepository->filter($request->all(), 'refund');
          if($request->id == 'desc'){
            $totalItems = $invoices->total();
            $index = $totalItems - ($invoices->currentPage() - 1) * $invoices->perPage();
          }else{
            $index = ($invoices->currentPage() - 1) * $invoices->perPage() + 1;
          }
          return view('admin.invoice.refund',compact('invoices','index'));
    }

    public function create()
    {
        $prods = $this->getProdFromWp();
        $students = Account::with('parent')->where('type', 'student')->get();
        $classes = Classes::with(['student.student','products'])
                    ->where('status', 'active')
                    ->orderBy('id','desc')
                    ->get();
        return view('admin.invoice.create', compact('students', 'prods','classes'));
    }

    public function store(UpdateInvoiceRequest $request)
    {
        $directory = storage_path('app/public/invoices');
        $quantities = json_decode($request->quantities, true);
        $customNames = json_decode($request->custom_names, true);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        $batchId = uniqid('inv_', true) . '_' . auth('admin')->id();
        $dataJob = [];
        DB::beginTransaction();
        try {
            $prods = $this->invoiceService->getProducts($request->prod);
            foreach ($prods as $prod) {
                $prods[$prod->id] = $prod;
            }
            $date = now();
            $students = Account::with('parent')->where('type', 'student')->whereIn('id', $request->student)->get();
            foreach ($students as $student) {
                $total = 0;
                foreach ($request->prod as $prodId) {
                    $quantity = $quantities[$student->id][$prodId] ?? 1;
                    $total += ($prods[$prodId]->price ?? $prods[$prodId]['price']) * $quantity;
                }
                $data = [
                    'status' => 'unpaid',
                    'parent_id' => $student->parent->id,
                    'batch_id' => $batchId,
                    'price' => $total,
                    'created_at' => $date,
                    'updated_at' => $date,
                    'processing_status' => 'Pending',
                ];
                $invoice = Invoice::create($data);
                InvoiceStudent::create([
                    'invoice_id' => $invoice->id,
                    'student_id' => $student->id,
                ]);
                $dataJob[] = [
                    'invoice_id' => $invoice->id,
                    'product_ids' => $request->prod,
                ];
                foreach ($request->prod as $prodId) {
                    $quantity = !empty($quantities[$student->id][$prodId]) ? $quantities[$student->id][$prodId] : 1;
                    $name = !empty($customNames[$student->id][$prodId]) ? $customNames[$student->id][$prodId] : $prods[$prodId]->name ?? $prods[$prodId]['name'];
                    ProductInvoice::create([
                        'invoice_id' => $invoice->id,
                        'prod_id' => $prodId,
                        'prod_name' => $name,
                        'prod_des' => $prods[$prodId]->description ?? $prods[$prodId]['description'],
                        'price' => $prods[$prodId]->price ?? $prods[$prodId]['price'],
                        'quantity' => $quantity,
                        'created_at' => $date,
                        'updated_at' => $date,
                    ]);
                }
            }
            DB::commit();
            if (!empty($dataJob)) {
                ProcessInvoiceJob::dispatch($dataJob);
            }
            return redirect(route('admin.invoices.index'))->with('success', 'Created successfully');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withInput()->with('error', 'Error');
        }
    }

    public function show($id){
      $invoice = $this->invoiceRepository->find($id);
      return view('admin.invoice.show',compact('invoice'));
    }

    public function getProdFromWp()
    {
        $client = new GuzzleHttp\Client();
        $all_products = [];
        $page = 1;
        $perPage = 100;
        $prods = [];
        do {
            $response = $client->get(config('app.wphost') . '/wp-json/wc/v3/products', [
                'auth' => [
                    config('app.wpkey'),
                    config('app.wpsecret')
                ],
                'query' => [
                    'per_page' => $perPage,
                    'page' => $page,
                    'orderby'  => 'id',
                    'order'    => 'desc',
                ],
            ]);
            $wpProds = json_decode($response->getBody()->getContents());
            $all_products = array_merge($all_products, $wpProds);
            $page++;
        } while (count($wpProds) > 0);

        foreach ($all_products as $val) {
            $prods[$val->id]['id'] = $val->id;
            $prods[$val->id]['name'] = $val->name;
            $prods[$val->id]['des'] = $val->description;
            $prods[$val->id]['price'] = $val->price;
        }
        return $prods;
    }
    public function syncWp(){
      $client = new GuzzleHttp\Client();
      $response = $client->post(config('app.wphost').'/wp-json/wc/v3/orders/32/refunds', [
        'content-type' => 'application/json',
        'auth' => [
          config('app.wpkey'),
          config('app.wpsecret')
        ]
      ]);
      $body = $response->getBody();
      $result = json_decode($body->getContents());
      dd($result);

      $client = new GuzzleHttp\Client();
      $invoices = $this->invoiceRepository->where(function ($query) {
        $query->where('status', 'request_refund')->orwhere('status', 'unpaid');
      })->get();

      foreach ($invoices as $invoice){
        $response = $client->get(config('app.wphost').'/wp-json/wc/v3/orders/'.$invoice->order_id.'?ver'.time(), [
          'auth' => [
            config('app.wpkey'),
            config('app.wpsecret')
          ],
        ]);
        $body = $response->getBody();

        $wpOrder = json_decode($body->getContents());
        $status = $invoice->status;
        if($wpOrder->status == 'pending' && $status != 'request_refund'){
          $status = 'unpaid';
        }elseif($wpOrder->status == 'completed' && $status != 'request_refund'){
          $status = 'paid';
        }elseif($wpOrder->status == 'refunded'){
          $status = 'refund';
        }

        if($status != $invoice->status){
          $this->invoiceRepository->update(['status'=>$status],$invoice->id);
        }
        Log::info('syncwp - id:'.$invoice->id);
        Log::info('syncwp - status:'.$wpOrder->status);
      }
      return 'done';
    }


    public function getProdByID($id){
      $prods = $this->getProdFromWp();
      $prod = $prods[$id];
      return response()->json([
        'status' => (bool)$prod,
        'prod'=> $prod
      ]);
    }
    public function edit($id){
      $prods = $this->getProdFromWp();
      $parents = $this->accountRepository->findWhere(['type'=> 'parent']);
      $invoice = $this->invoiceRepository->find($id);
      $prodInvoice = array();
      foreach ($invoice->product as $key => $val){
        $prodInvoice[$val->id]['des'] = $val['prod_des'];
        $prodInvoice[$val->id]['price'] = $val['price'];
        $prodInvoice[$val->id]['name'] = $val['prod_name'];
      }
      return view('admin.invoice.edit',compact('invoice', 'parents', 'prods', 'prodInvoice'));
    }

    public function update(Request $request,$id){
      DB::beginTransaction();
      try {
        $data['status'] =$request->status;
        $this->invoiceRepository->update($data,$id);
        $mess = $request->status == 'refund' ? "Refund successfully" : "Cancel refund successfully";
        DB::commit();
        return redirect(route('admin.invoices.refunds'))->with('success',$mess);
      }
      catch(\Exception $e) {
        DB::rollback();
        return redirect()->back()->withInput()->with('error','Error');
      }
    }

    public function destroy($id){
      $invoice  = $this->invoiceRepository->find($id);
      if($invoice->status == 'unpaid'){
        $invoice->delete();
        $invoice->product()->delete();
        $invoice->students()->delete();
        return redirect(route('admin.invoices.index'))->with('success','Deleted successfully');
      }
      abort('404');
    }

    public function invoicePdf(Request $request, $id)
    {
        $requestData = $request->all();
        $quantities = explode(',', $request->quantities);
        $parent = Account::with([
            'student' => function ($query) use ($requestData) {
                $query->when(!empty($requestData['student_id']), function ($query) use ($requestData) {
                    $query->where('id', $requestData['student_id']);
                });
            }
        ])
        ->where('type', 'parent')
        ->find($id);
        if($request->ids == null) abort('404');
        $ids = explode(',', $request->ids);
        $options = $this->invoiceService->getOptions();
        $options['quantities'] = $quantities;
        $options['custom_names'] = json_decode($request->custom_names, true);
        $pdf = $this->invoiceService->getInvoicePdf($parent, $ids,$options);
        return $pdf->stream('invoice.pdf');
    }


    public function detail(Request $request, $id)
    {
        $parent = Account::with('student')->where('type', 'parent')->find($id);
        if($request->ids == null) abort('404');
        $ids = explode(',', $request->ids);
        $quantities = explode(',', $request->quantities);
        $customNames = json_decode($request->custom_names, true);
        $products = $this->invoiceService->getProducts($ids);
        return view('admin.invoice.detail',compact('parent', 'products', 'quantities', 'customNames'));
    }

    public function batch() {
        $products = $this->getProdFromWp();
        $students = Account::with([
            'parent',
            'classes.products',
            'invoiceStudent.invoice' => function($query) {
                $query->where('processing_status', 'Completed');
            },
            'invoiceStudent.invoice.product'
        ])->where('type', 'student')->orderBy('id','desc')->get();
        foreach ($students as $student) {
            $classProductIds = $student->classes
                ->flatMap(fn($class) => $class?->products?->pluck('product_id'))
                ->unique()
                ->values();

            $paidProductIds = $student->invoiceStudent
                ->flatMap(fn($invoice) => $invoice?->invoice?->product->pluck('prod_id'))
                ->unique()
                ->values();
            $student->name = $student->name_english;
            $student->parent_name = $student->parent?->name_english.' - '.$student?->parent?->phone;
            $student->parent_id = $student->parent?->id;
            $student->products = $classProductIds->diff($paidProductIds)->values();
        }

        return view('admin.grades.grades',compact('products','students'));
    }

}
