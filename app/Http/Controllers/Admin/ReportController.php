<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\AccountRepository;
use App\Repositories\ClassRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\ReportRepository;
use App\Repositories\StudentClassRepository;
use App\Repositories\TermRepository;
use App\Services\KakaoMessageService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class ReportController extends Controller
{
    public $classRepository;
    public $accountRepository;
    public $studentClassRepository;
    public $reportRepository;
    public $termRepository;
    public $classScheduleRepository;
    public function __construct(ClassScheduleRepository $classScheduleRepository,ClassRepository $classRepository,AccountRepository $accountRepository,StudentClassRepository $studentClassRepository,ReportRepository $reportRepository,TermRepository $termRepository){
      $this->classRepository = $classRepository;
      $this->accountRepository = $accountRepository;
      $this->studentClassRepository = $studentClassRepository;
      $this->reportRepository = $reportRepository;
      $this->termRepository = $termRepository;
      $this->classScheduleRepository = $classScheduleRepository;
    }
    public function index(Request $request){
      $classes = $this->classRepository->where('status', 'active')
                    ->orderBy('id','desc')
                    ->get();;
      $students = $this->accountRepository->where('type','student');
      if($request->keyword){
        $keyword = convertKeyword($request->keyword);
        $students = $students->where(function ($query) use ($keyword){
          $query->where('name_english','like', "%".$keyword."%")->orwhere('name_korea','like', "%".$keyword."%");
        });
      }
      if($request->class){
        $students = $students->whereHas('classes', function ($query) use ($request){
          $query->where('class_id', '=', $request->class);
        });
      }
      $key = 'created_at';
      $value = 'desc';
      if($request->korea == 'asc' || $request->korea == 'desc'){
          $key = 'name_korea';
          $value = $request->korea;
      }else if($request->english == 'asc' || $request->english == 'desc'){
          $key = 'name_english';
          $value = $request->english;
      }
      $students = $students->orderBy($key, $value)->paginate(10);
      return view('admin.reports.index',compact('classes','students'));
    }

    public function show(Request $request,$id){
      $term = [];
      $edit = [];
      $status1 = null;
      $status5 = null;
      $status9 = null;
      $status13 = null;
      $student = $this->accountRepository->where('type','student')->find($id);
      $type = $request->type;
      if ($student) {
        if($request->type == 'private'){
          $classes = $this->studentClassRepository
            ->with('classes')
            ->whereHas('classes', function ($query) {
              $query->where('type', '=', 'Private');
            })
            ->orderBy('id','desc')
            ->findWhere(['student_id' => $id]);
          $reports = array();

          foreach ($classes as $key => $val){
            $r = $this->reportRepository->where('type', 'private')->where('term_id', $val->classes->id)->where('student_id', $id)->get();
            $temp['class'] = $val->classes;
            $temp['report'] = $r;
            array_push($reports, $temp);
          }
          $perPage = 10;
          $currentPage = LengthAwarePaginator::resolveCurrentPage();
          $currentItems = collect($reports)->slice(($currentPage - 1) * $perPage, $perPage)->all();
          $reports = new LengthAwarePaginator($currentItems, count($reports), $perPage);
          $reports->withQueryString();
          $reports->setPath($request->url());
          return view('admin.reports.details-private', compact('classes', 'type', 'reports', 'student'));
        }else {
          $classes = $this->studentClassRepository
            ->with('classes')
            ->whereHas('classes', function ($query) {
              $query->where('type', '=', 'Regular');
            })
            ->orderBy('id','desc')
            ->findWhere(['student_id' => $id])
            ->groupBy('classes.term');
          $perPage = 1;
          $currentPage = LengthAwarePaginator::resolveCurrentPage();
          $currentItems = collect($classes)->slice(($currentPage - 1) * $perPage, $perPage)->all();
          $classes = new LengthAwarePaginator($currentItems, count($classes), $perPage);
          $classes->withQueryString();
          $classes->setPath($request->url());
          if (!empty($classes->keys()->first()) && !empty($classes->values()[0][0]->class_id) && !empty($classes->values()[0][0]->classes->terms)) {
            $term = $classes->values()[0][0]->classes->terms;
            $cl_id = $classes->values()[0][0]->class_id;
            $class = $this->classRepository->with(['schedule'])->whereHas('terms',function ($q) use ($term){
              $q->where('term',$term->id);
            })->whereHas('schedule',function ($q) use ($cl_id){
              $q->where('class_id',$cl_id);
            })->findWhere(['term'=>$classes->keys()->first()])->first();
            if($class){
              $schedule = $this->classScheduleRepository->getSesion($class->schedule);
              foreach ($schedule as $sche){
                if($sche->session == 1 && $sche->date <= now()){
                  $edit[1] = true;
                }else if($sche->session == 5 && $sche->date <= now()){
                  $edit[5] = true;
                }else if($sche->session == 9 && $sche->date <= now()){
                  $edit[9] = true;
                }else if($sche->session == 13 && $sche->date <= now()){
                  $edit[13] = true;
                }
              }
            }
            $status1 = $this->reportRepository->where('term_id', $term->id)->where('session', 1)->where('student_id', $id)->where('type', 'regular')->first();
            $status5 = $this->reportRepository->where('term_id', $term->id)->where('session', 5)->where('student_id', $id)->where('type', 'regular')->first();
            $status9 = $this->reportRepository->where('term_id', $term->id)->where('session', 9)->where('student_id', $id)->where('type', 'regular')->first();
            $status13 = $this->reportRepository->where('term_id', $term->id)->where('session', 13)->where('student_id', $id)->where('type', 'regular')->first();
          }
          $terms=['T1'=>'Term 1','T2'=>'Term 2','T3'=>'Term 3'];
          return view('admin.reports.details', compact(
            'classes', 'student', 'term', 'terms', 'status1', 'status5', 'status9', 'status13', 'type','edit'
          ));
        }
      }
      abort('404');
    }

    public function edit(Request $request,$id){
      if($request->type == 'private'){
        $report = $this->reportRepository->where('type', 'private')->where('id', $request->report_id)->first();
        $class = $this->classRepository->findWhere(['id' => $request->term_id])->first();
        $schedule = $class->schedule->groupBy('date')->toArray();
        if(!$report){
          foreach ($schedule as $key =>$value){
            if($this->reportRepository->where('type', 'private')->where('date_private', $key)->where('term_id', $request->term_id)->where('student_id', $id)->first()){
              unset($schedule[$key]);
            }
          }
        }
        return view('admin.reports.edit-private',compact('class','report', 'id', 'schedule'));
      }else{
        $classes = $this->studentClassRepository
          ->with('classes')
          ->whereHas('classes', function ($query) use ($request) {
            $query->where('type', '=', 'Regular')
              ->where('term', '=', $request->term_id);
          })
          ->findWhere(['student_id' => $id]);
        if(!count($classes) || !in_array($request->session,[1,5,9,13])){
          abort('404');
        }
        $session = $request->session;
        $report = $this->reportRepository->where('term_id',$request->term_id)->where('session',$session)->where('student_id',$id)->first();
        $term = $this->termRepository->find($request->term_id);
        return view('admin.reports.edit',compact('classes','session','report','term','id'));
      }
    }

    public function update(Request $request,$id, KakaoMessageService $kakaoMessageService){
      $type = 'regular';
      if($request->type == 'private'){
        Log::info('Updating private report for student ID: ' . $id);
        $this->validatePrivate($request);
        $type = 'private';
        $data = $request->all();
        unset($data['ty']);
        $data['student_id'] = $id;
        $conditions = [
          'term_id' => $request->term_id,
          'type' => 'private',
          'student_id' => $id,
          'date_private' => $request->date_private,
        ];
        $data['reading'] = trimTextArea($request->reading,1000);
        $report = $this->reportRepository->findWhere($conditions)->first();
        if ($report) {
          $this->reportRepository->update($data, $report->id);
        } else {
          $this->reportRepository->create($data);
        }
        $account =  $this->accountRepository->find($id);
        $parent = $this->accountRepository->findWhere(['type'=>'parent','user_id'=>$account->user_id])->first();
        $date = Carbon::parse($request->date_private);
        $month = $date->month; 
        $day   = $date->day; 
        $message= "($account->user_name)의 {$month}월 {$day}일 수업의 피드백이 업데이트 되었습니다. 확인 부탁드립니다.";
        if($request->status == 'active'){
          $kakaoMessageService->sendKakaoMessagesForParentOfStudent($parent, $message);
        }
      }else {
        $this->validatePublic($request);
        $conditions = [
          'term_id' => $request->term_id,
          'session' => $request->session,
          'type' => 'regular',
          'student_id' => $id,
        ];
        $data = $request->all();
        $data['type'] = 'regular';
        $data['student_id'] = $id;
        $data['writing'] = trimTextArea($request->writing,1000);
        $data['reading'] = trimTextArea($request->reading,1000);
        $report = $this->reportRepository->findWhere($conditions)->first();
        if ($report) {
          $this->reportRepository->update($data, $report->id);
        } else {
          $this->reportRepository->create($data);
        }
      }
      $message = isset($request->ty) ? $request->ty.' successfully' : 'Updated successfully';
      return redirect(route('admin.reports.show',$id).'?type='.$type)->with('success', $message);
    }
    public function destroy(Request $request, $id){
      $type = 'regular';
      if($request->type == 'private') {
        $type = 'private';
      }
      $this->reportRepository->delete($request->report_id);
      return redirect(route('admin.reports.show',$id).'?type='.$type)->with('success','Delete successfully');
    }

  public function validatePublic(Request $request){
    $rules = [
      'reading' => 'required',
      'writing' => 'required',
      'status' => 'required|in:active,inactive',
    ];


    $message = [
      'reading.required' => 'This is a required field.',
      'writing.required' => 'This is a required field.',
      'status.required' => 'This is a required field.',
      'status.in' => 'Invalid format.',
    ];

    $request->validate($rules,$message);
  }
  public function validatePrivate(Request $request){
    $rules = [
      'date_private' => 'required',
      'reading' => 'required',
      'status' => 'required|in:active,inactive',
    ];


    $message = [
      'reading.required' => 'This is a required field.',
      'date_private.required' => 'This is a required field.',
      'status.required' => 'This is a required field.',
      'status.in' => 'Invalid format.',
    ];

    $request->validate($rules,$message);
  }
}
