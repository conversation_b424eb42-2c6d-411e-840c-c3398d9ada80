<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\AccountUpdateRequest;
use App\Http\Requests\CreateAccountRequest;
use App\Models\AccountHomework;
use App\Models\AccountHomeworkTask;
use App\Models\AccountMainHomework;
use App\Models\GradeHomework;
use App\Models\RequestUnlock;
use App\Repositories\AccountRepository;
use App\Repositories\ClassRepository;
use App\Repositories\InitTestRepository;
use App\Repositories\StudentClassRepository;
use App\Repositories\User\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class StudentController extends Controller
{
  protected $userRepository;
  protected $accountRepository;
  protected $initTestRepository;
  protected $classRepository;
  protected $studentClassRepository;

  public function __construct(UserRepository $userRepository,AccountRepository $accountRepository,InitTestRepository $initTestRepository,ClassRepository $classRepository,StudentClassRepository $studentClassRepository)
  {
    $this->userRepository = $userRepository;
    $this->accountRepository = $accountRepository;
    $this->initTestRepository = $initTestRepository;
    $this->classRepository = $classRepository;
    $this->studentClassRepository = $studentClassRepository;
  }
  /**
   * Display a listing of the resource.
   */
  public function index(Request $request)
  {
    $students = $this->accountRepository->filter($request->all());
    if($request->no == 'desc'){
      $totalItems = $students->total();
      $index = $totalItems - ($students->currentPage() - 1) * $students->perPage();
    }else{
      $index = ($students->currentPage() - 1) * $students->perPage() + 1;
    }
    $classes = $this->classRepository
                    ->where('status', 'active')
                    ->orderBy('id','desc')
                    ->get();

    $response = Http::get('https://countriesnow.space/api/v0.1/countries/codes');
    $countries = [];
    if ($response->successful()) {
        $countries = $response->json()['data'];
    }
    return view('admin.students.index',compact('students','index','classes', 'countries'));
  }

  /**
   * Show the form for creating a new resource.
   */
  public function create()
  {
    $parents = $this->accountRepository->findWhere(['type'=> 'parent']);

    return view('admin.students.create', compact('parents'));

  }

  /**
   * Store a newly created resource in storage.
   */
    public function store(CreateAccountRequest $request)
    {
        $dataUser = [
            'name' => truncate_string($request->parent_name),
            'email' => $request->email_parent,
            'status' => 'inactive',
        ];
        $parent = null;

        DB::beginTransaction();
        try {
            if ($request->country) {
                list($request->country, $request->states, $request->dial_code) = explode('=', $request->country);
            }
            if ($request->parent) {
                $user = $this->accountRepository->with(['user'])->find($request->parent);
                if (!$user->country_id && $request->country) {
                    list($request->country, $request->states, $request->dial_code) = explode('=', $request->country);
                    $user->update([
                        'country_id' => $request->country,
                        'city_id' => $request->city,
                        'states' => $request->states,
                        'dial_code' => $request->dial_code,
                        'phone_code' => $request->phone_code,
                    ]);
                };
                $parent = $user;
                $user = $user->user;
            } else {
                $user = $this->userRepository->create($dataUser);
                $dataParent = [
                    'user_id' => $user->id,
                    'type' => 'parent',
                    'name_english' => truncate_string($request->parent_name),
                    'phone' => $request->phone_parent,
                    'relationship' => $request->relationship,
                    'relationship_other' => !empty($request->relationship_other) && $request->relationship == 'Other' ? $request->relationship_other : null,
                    'address' => $request->address,
                    'email' => $request->email_parent,
                    'country_id' => $request->country,
                    'city_id' => $request->city,
                    'states' => $request->states,
                    'dial_code' => $request->dial_code,
                    'phone_code' => $request->phone_code,
                ];
                $this->accountRepository->create($dataParent);
            }

            $dataStudent = [
                'user_id' => $user->id,
                'type' => 'student',
                'phone' => $request->phone_student,
                'start_date' => $request->start_date,
                'level' => $request->level,
                'name_english' => truncate_string($request->name_english),
                'name_korea' => truncate_string($request->name_korea),
                'birthday' => $request->birthday,
                'gender' => $request->gender,
                'email' => $request->email_student,
                'school' => $request->school,
                'grade' => $request->grade,
                'country_id' => $request->country,
                'city_id' => $request->city,
                'states' => $request->states,
                'dial_code' => $request->dial_code,
                'phone_code' => $request->phone_code ?? (!empty($parent) ? $parent->phone_code : null),
            ];
            $student = $this->accountRepository->create($dataStudent);
            $dataInitTest = [
                [
                    'user_id' => $student->id,
                    'subject' => 'Reading',
                ],
                [
                    'user_id' => $student->id,
                    'subject' => 'Writing',
                ],
            ];
            $this->initTestRepository->insert($dataInitTest);
            if ($request->country) {
                $this->accountRepository->getModel()->where('user_id', $user->id)->update([
                    'country_id' => $request->country,
                    'city_id' => $request->city,
                    'states' => $request->states,
                    'dial_code' => $request->dial_code,
                ]);
            }
            DB::commit();

            return redirect()->route('admin.students.index')->with('success', 'Created successfully');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withInput()->with(['error' => 'Serve Error']);
        }
    }

  /**
   * Display the specified resource.
   */
  public function show(string $id)
  {
    $countries = $this->getCountries();
    $student = $this->accountRepository->with(['parent','initTest','classes'])->findWhere(['type'=>'student','id'=>$id])->first();
    $classes = $this->classRepository->where('status','active')->get();
    if($student){
      return view('admin.students.show',compact('student','countries','classes'));
    }
  }

  /**
   * Show the form for editing the specified resource.
   */
  public function edit(string $id)
  {
    $student = $this->accountRepository->with(['parent','initTest','classes'])->findWhere(['type'=>'student','id'=>$id])->first();
    $classes = $this->classRepository->where('status','active')->get();
    if($student){
      return view('admin.students.edit',compact('student','classes'));
    }
  }

  /**
   * Update the specified resource in storage.
   */
    public function update(AccountUpdateRequest $request, string $id)
    {
        if ($request->country) {
            list($request->country, $request->states, $request->dial_code) = explode('=', $request->country);
        }
        DB::beginTransaction();
        try {
            $dataStudent = [
                'type' => 'student',
                'phone' => $request->phone_student,
                'start_date' => $request->start_date,
                'level' => $request->level,
                'name_english' => truncate_string($request->name_english),
                'name_korea' => trim($request->name_korea),
                'birthday' => $request->birthday,
                'gender' => $request->gender,
                'email' => $request->email_student,
                'school' => $request->school,
                'grade' => $request->grade,
                'country_id' => $request->country,
                'city_id' => $request->city,
                'states' => $request->states,
                'dial_code' => $request->dial_code,
                'date_of_exam' => $request->date_of_exam,
                'time_of_exam' => $request->time_of_exam,
                'comment_of_exam' => trimTextArea($request->comment_of_exam, 1000),
                'phone_code' => $request->phone_code,
            ];
            $dataParent = [
                'name_english' => $request->parent_name,
                'phone' => $request->phone_parent,
                'email' => $request->email_parent,
                'address' => $request->address,
                'relationship' => $request->relationship,
                'relationship_other' => $request->relationship_other,
                'phone_code' => $request->phone_code,
            ];
            if ($request->country) {
                $dataParent['country_id'] = $request->country;
                $dataParent['city_id'] = $request->city_id;
                $dataParent['states'] = $request->states;
                $dataParent['dial_code'] = $request->dial_code;
            }
            $dataReading = [
                'score' => $request->score_reading,
                'score1' => $request->score1,
                'score2' => $request->score2,
                'recommended' => trimTextArea($request->reading_recommended, 500),
                'analysis' => trimTextArea($request->reading_analysis, 500),
            ];
            $reading = $this->initTestRepository->where('user_id', $id)
                ->where('subject', 'Reading')
                ->first();
            $writing = $this->initTestRepository->where('user_id', $id)->where('subject', 'Writing')->first();
            if ($reading) {
                $reading->update($dataReading);
            }
            if ($writing) {
                $dataWriting = [
                    'score' => $request->score_writing + $request->essay_tructure + $request->support_detail + $request->diction + $request->construction,
                    'essay_tructure' => $request->essay_tructure,
                    'support_detail' => $request->support_detail,
                    'diction' => $request->diction,
                    'construction' => $request->construction,
                    'analysis' => trimTextArea($request->writing_analysis, 500),
                    'recommended' => trimTextArea($request->writing_recommended, 500),
                ];
                $writing->update($dataWriting);
            }

            $student = $this->accountRepository->update($dataStudent, $id);
            $this->accountRepository->where('type', 'parent')->where('user_id', $student->user_id)->update($dataParent);
            $this->userRepository->findOrFail($student->user_id)->update(['email' => $request->email_parent]);
            if ($request->country) {
                $this->accountRepository->getModel()->where('user_id', $student->user_id)->update([
                    'country_id' => $request->country,
                    'city_id' => $request->city,
                    'states' => $request->states,
                    'dial_code' => $request->dial_code,
                ]);
            }
            if (!empty($request->class)) {
                $student->classes()->sync($request->class);
            } else {
                $this->studentClassRepository->deleteWhere([
                    'student_id' => $student->id,
                ]);
            }
            DB::commit();
            return redirect()->route('admin.students.index')->with('success', 'Updated successfully');
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->withInput()->with(['error' => 'Serve Error']);
        }
    }

  /**
   * Remove the specified resource from storage.
   */
    public function destroy(string $id)
    {
        $student = $this->accountRepository->find($id);
        $student->update([
            'email' => $student->email . '-' . time(),
            'phone' => $student->phone . '-' . time(),
        ]);
        $student2 = $this->accountRepository->findWhere(['user_id' => $student->user_id, 'type' => 'student', ['id', '!=', $student->id]])->first();
        if (!$student2) {
            $parent = $this->accountRepository->findWhere(['user_id' => $student->user_id, 'type' => 'parent'])->first();
            $parent->update([
                'email' => $parent->email . '-' . time(),
                'phone' => $parent->phone . '-' . time(),
            ]);
            $userTotal = $this->userRepository->find($parent->user_id);
            $userTotal->update([
                'email' => $userTotal->email . '-' . time(),
            ]);
            $userTotal->delete();
            $parent->delete();
        }
        $student->delete();
        AccountMainHomework::where('account_id', $id)->delete();
        AccountHomework::where('account_id', $id)->delete();
        AccountHomeworkTask::where('account_id', $id)->delete();
        GradeHomework::where('account_id', $id)->delete();
        RequestUnlock::where('account_id', $id)->delete();
        return redirect()->back()->with('success', 'Deleted successfully');
    }

  public function getCities($id){
    $path = './src/js/cities.json';
    $cities = json_decode(file_get_contents($path), true);
    $chooseCity = [];
    foreach ($cities as $city){
      if($city['country_id'] == $id){
        $chooseCity[] = $city;
      }
    }
    return response()->json($chooseCity);
  }

  public function getParentByID($id){
    $parent = $this->accountRepository->with(['student'])->findWhere([
      'id'=> $id,
      'type'=> 'parent'
    ])->first();
    if($parent){
      $generatedNames = [];
      foreach ($parent->student as $student) {
        if (empty($student->account_name)) {
          $birthday = date('md', strtotime($student->birthday));
          $accountName = $student->name_english . $birthday;
          $count = $this->accountRepository->where('user_name', $accountName)->count();
          if (in_array($accountName, $generatedNames) || $count > 0) {
            $suffix = 1;
            while (in_array($accountName . $suffix, $generatedNames) || $this->accountRepository->where('user_name', $accountName . $suffix)->count() > 0) {
              $suffix++;
            }
            $accountName .= $suffix;
          }
          $student->account_name = Str::slug($accountName, '');
          $generatedNames[] = $student->account_name;
        }
      }
    }
    return response()->json([
      'status' => (bool)$parent,
      'parent'=> $parent
    ]);
  }

  public function getCountries(){
    $path = './src/js/countries.json';
    $countries = json_decode(file_get_contents($path), true);
    $specialCountries = ['South Korea', 'China', 'Vietnam', 'Singapore', 'India'];

    $otherCountries = [];

    $sortedSpecialCountries = [];

    foreach ($countries as $country) {
      if (in_array($country['name'], $specialCountries)) {
        $index = array_search($country['name'], $specialCountries);
        $sortedSpecialCountries[$index] = $country;
      } else {
        $otherCountries[] = $country;
      }
    }

    ksort($sortedSpecialCountries);

    $countries = array_merge($sortedSpecialCountries, $otherCountries);
    return $countries;
  }

  public function getStudentByClassId(Request $request){
    $condition= [];
    if($request->id){
      $condition['class_id'] = $request->id;
    }
    $students = $this->studentClassRepository->with('student')->whereHas('student',function ($q){
      $q->where('type','student');
    })->findWhere($condition);
    return response()->json($students);
  }

  public function getClassByStudentID(Request $request){
    $condition= [];
    if($request->id){
      $condition['student_id'] = $request->id;
    }
    $classes = $this->studentClassRepository->with(['classes' => function ($query) {
      $query->where('type', 'Regular');
    }])->findWhere($condition);
    return response()->json($classes);
  }
}
