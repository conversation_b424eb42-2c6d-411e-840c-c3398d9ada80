<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateScheduleRequest;
use App\Models\MainHomeworks;
use App\Repositories\ClassRepository;
use App\Repositories\ClassScheduleRepository;
use App\Services\MainHomeWorkService;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class ScheduleController extends Controller
{
  public $classRepository;
  public $classScheduleRepository;

  public function __construct(ClassRepository $classRepository, ClassScheduleRepository $classScheduleRepository, protected MainHomeWorkService $mainHomeWorkService)
  {
    $this->classRepository = $classRepository;
    $this->classScheduleRepository = $classScheduleRepository;
  }

  public function index(Request $request)
  {
    $classes = $this->classRepository->where('status', 'active')
                  ->orderBy('id','desc')
                  ->get();
    $schedules = $this->classScheduleRepository
      ->with('classes.terms');
    if ($request->class) {
      $schedules = $schedules->where('class_id', $request->class);
    }
    $schedules = $schedules->whereHas('classes', function ($query) {
      $query->where('status','active');
    });
    $schedules = $schedules->orderBy('class_id','desc')->get();
    $schedules = $this->classScheduleRepository->getSesion($schedules);
//    foreach ($schedules as $key => $val){
//      $start_class = $val->classes->type == 'Regular' ? $val->classes->terms->start : $val->classes->start_date;
//      $start_br = $val->classes->type == 'Regular' ? $val->classes->terms->break_start : null;
//      $schedules[$key]->session = getSession($start_class, $start_br, Carbon::parse('2024-05-06'));
//    }

    if ($request->session && $schedules) {
      $filteredSchedules = collect($schedules)->filter(function ($schedule) use ($request) {
        return $schedule['session'] == $request->session;
      });
      $schedules = $filteredSchedules;
    }

    $perPage = 10;
    $currentPage = LengthAwarePaginator::resolveCurrentPage();
    $currentItems = $schedules->slice(($currentPage - 1) * $perPage, $perPage)->all();
    $schedules = new LengthAwarePaginator($currentItems, count($schedules), $perPage);
    $schedules->withQueryString();
    $schedules->setPath($request->url());
    return view('admin.schedules.index', compact('classes', 'schedules'));
  }


  public function edit($id)
  {
    $schedule = $this->classScheduleRepository->with('classes')->find($id);
    return view('admin.schedules.edit', compact('schedule'));
  }

  public function show($id)
  {
    $schedule = $this->classScheduleRepository->with('classes')->find($id);
    return view('admin.schedules.show', compact('schedule'));
  }

  public function update(UpdateScheduleRequest $request, $id)
  {
    $data = $this->classScheduleRepository->find($id);
    $conflict = $this->classScheduleRepository->where('date', $request->date)->where('id', '!=', $id)->where('class_id', $data->class_id)->exists();
    if ($conflict) {
      return redirect()->back()->withInput()->with('error', 'The registration schedule has been duplicated');
    }
    $this->classScheduleRepository->update($request->all(), $id);
    return redirect()->route('admin.schedules.index', $id)->with('success', 'Updated successfully');
  }

    public function destroy($id)
    {
        $this->classScheduleRepository->delete($id);
        $mainHomework = MainHomeworks::where('schedule_id', $id)->first();
        if ($mainHomework) {
            $this->mainHomeWorkService->delete($id);
        }
        return redirect()->route('admin.schedules.index')->with('success', 'Delete successfully.');
    }

  public function session(Request $request){
    $schedules = $this->classScheduleRepository->findWhere(['class_id'=>$request->id]);
    $sSesion = $this->classScheduleRepository->getSesion($schedules);
    $lastSession = null;
    foreach ($sSesion as $schedule) {
      if (date('Y-m-d') >= $schedule->date) {
        $lastSession = $schedule->session;
      }
    }
    return response()->json([
      'session' => $lastSession,
    ]);
  }
}
