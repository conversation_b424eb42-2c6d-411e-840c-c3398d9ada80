<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\AttendeanceRequest;
use App\Jobs\SendKakaoMessageJob;
use App\Models\Attendance;
use App\Models\ClassSchedule;
use App\Models\GradeHomework;
use App\Models\MainHomeworks;
use App\Repositories\AccountRepository;
use App\Repositories\AttendanceRepository;
use App\Repositories\ClassRepository;
use App\Repositories\ClassScheduleRepository;
use App\Repositories\StudentClassRepository;
use Carbon\Carbon;
use Google\Service\Connectors\Schedule;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class AttendanceController extends Controller
{
    public $classRepository;
    public $classScheduleRepository;
    public $studentClassRepository;
    public $attendanceRepository;
    public $accountRepository;
    public function __construct(
      ClassRepository $classRepository,
      ClassScheduleRepository $classScheduleRepository,
      StudentClassRepository $studentClassRepository,
      AttendanceRepository $attendanceRepository,
      AccountRepository $accountRepository
      )
    {
      $this->classRepository = $classRepository;
      $this->classScheduleRepository = $classScheduleRepository;
      $this->studentClassRepository = $studentClassRepository;
      $this->attendanceRepository = $attendanceRepository;
      $this->accountRepository = $accountRepository;
    }
    public function regular(Request $request){
      $classes = $this->classRepository->findWhere(['type'=>'Regular','status'=>'active']);
      $data = [];
      $lastSession = 0;

      if ($request->class) {
        $schedules = $this->classScheduleRepository
          ->with('classes')
          ->where('class_id', $request->class)->orderBy('date')->get();
        $schedules = $this->classScheduleRepository->getSesion($schedules);
        $sessionItems = [];

        if (empty($request->session)) {
          foreach ($schedules as $schedule) {
            if (date('Y-m-d') >= $schedule->date) {
              $lastSession = $schedule->session;
            }
          }
        }
        $lastSession = $request->session ?: $lastSession;
        foreach ($schedules as $schedule) {
          if ($schedule['session'] == $lastSession ) {
            $sessionItems[] = $schedule;
          }
        }
        $studentClassInfo = $this->studentClassRepository;
        if ($request->name) {
          $keyw = convertKeyword($request->name);
          $studentClassInfo->with(['student' => function ($query) use ($keyw) {
            $query->where(function ($query) use ($keyw) {
              $query->where('user_name','like','%'.$keyw.'%')
                ->orWhere('name_english','like','%'.$keyw.'%');
            });
          }]);
        }
        if($request->term){
          $studentClassInfo->with(['classes' => function ($query) use ($request) {
            $query->where('term', $request->term);
          }]);
        }
        $studentClassInfo->with(['classes.schedule']);
        $studentClassInfo = $studentClassInfo->findWhere(['class_id' => $request->class]);
        foreach ($studentClassInfo as $studentInfo) {
          $student = $studentInfo->student;

          if($student && $studentInfo->classes){
            $classSchedule = $studentInfo->classes->schedule;
            $studentSchedules = [];
            foreach ($classSchedule as $schedule) {
              if (in_array($schedule->id, array_column($sessionItems, 'id'))) {
                $attended = $schedule->getAttendanceInfo($student->id);
                $studentSchedules[] = [
                  'schedule' => $schedule,
                  'attended' => $attended
                ];
              }
            }
            $data[] = [
              'student' => $student,
              'schedules' => $studentSchedules,
              'created_at'=> $studentInfo->created_at->format('Y-m-d'),
            ];
          }
        }

      }
      $date = date('Y-m-d');
      if(!empty($sessionItems[0])){
        $date = $sessionItems[0]->date;
      }
      $timestamp = strtotime($date);
      $firstDayOfWeek = strtotime('last sunday', $timestamp);
      $cl = $this->classRepository->where('id',$request->class)->first();
      $term['year'] = '';
      $term['term'] = '';
      if($cl){
        $term['year'] = $cl->terms->year;
        $term['term'] = $cl->terms->type;
      }
      $daysOfWeek= [];
      if($cl){
        $i = 1;
        while (count($daysOfWeek) < 7) {
          $currentDay = strtotime("+$i days", $firstDayOfWeek);
          if (!(($currentDay >= strtotime($cl->terms->break_start) && $currentDay <= strtotime($cl->terms->break_end)) ||
            ($cl->terms->break_option_start && ($currentDay >= strtotime($cl->terms->break_option_start) && $currentDay <= strtotime($cl->terms->break_option_end))))) {
            $daysOfWeek[] = date('m/d', $currentDay);
          }
          $i++;
        }
      }else{
        for ($i = 1; $i <= 7; $i++) {
          $daysOfWeek[] = date('m/d', strtotime("+$i days", $firstDayOfWeek));
        }
      }
      $perPage = 10;
      $currentPage = LengthAwarePaginator::resolveCurrentPage();
      $currentItems = collect($data)->slice(($currentPage - 1) * $perPage, $perPage)->all();
      $data = new LengthAwarePaginator($currentItems, count($data), $perPage);
      $data->withQueryString();
      $data->setPath($request->url());

      return view('admin.attendances.regular.index',compact('classes','data','daysOfWeek','lastSession', 'term','cl'));
    }

    public function edit(Request $request,$id){
      if($request->class && $id){
        $student = $this->studentClassRepository->with(['student'])->findWhere(['student_id'=> $id,'class_id'=>$request->class])->first();
        if($student){
          return view('admin.attendances.regular.edit',compact('student'));
        }
      }
      return redirect()->route('admin.attendance.regular');
    }

    public function store(AttendeanceRequest $request){
      if($request->user_id && $request->class_id){
        $data = $this->studentClassRepository->with('classes')->findWhere(['student_id'=> $request->user_id,'class_id'=> $request->class_id])->first();
        if($data){
          $dataSchedule = $this->classScheduleRepository->findWhere(['class_id'=> $request->class_id,'date'=> $request->date])->first();
          if($dataSchedule){
            $dataAtt = $this->attendanceRepository->findWhere(['user_id'=> $request->user_id,'schedule_id'=> $dataSchedule->id])->first();
            if($dataAtt){
              $this->attendanceRepository->update(['status'=> $request->type],$dataAtt->id);
            }else{
              $this->attendanceRepository->create(['user_id'=> $request->user_id,'schedule_id'=> $dataSchedule->id,'status'=> $request->type]);
            }
            return redirect()->route('admin.attendance.regular', ['class' => $request->class_id])->with('success','Updated successfully.');
          }
        }
      }
      return redirect()->back()->withInput()->with('error','The class does not exist');
    }


    public function special(Request $request){
      $classes = $this->classRepository->findWhere(['type'=>'Special']);
      $data = [];
      $lastSession = 0;

      if ($request->class) {
        $schedules = $this->classScheduleRepository
          ->with('classes')
          ->where('class_id', $request->class)->orderBy('date')->get();
        $schedules = $this->classScheduleRepository->getSesion($schedules);
        $sessionItems = [];

        if (empty($request->session)) {
          foreach ($schedules as $schedule) {
            if (date('Y-m-d') >= $schedule->date) {
              $lastSession = $schedule->session;
            }
          }
        }
        $lastSession = $request->session ?: $lastSession;
        foreach ($schedules as $schedule) {
          if ($schedule['session'] == $lastSession ) {
            $sessionItems[] = $schedule;
          }
        }
        $studentClassInfo = $this->studentClassRepository;
        if ($request->name) {
          $studentClassInfo->with(['student' => function ($query) use ($request) {
            $query->where(function ($query) use ($request) {
              $query->where('user_name', $request->name)
                ->orWhere('name_english', $request->name);
            });
          }]);
        }
        $studentClassInfo->with(['classes.schedule']);
        $studentClassInfo = $studentClassInfo->findWhere(['class_id' => $request->class]);
        foreach ($studentClassInfo as $studentInfo) {
          $student = $studentInfo->student;

          if($student && $studentInfo->classes){
            $classSchedule = $studentInfo->classes->schedule;
            $studentSchedules = [];
            foreach ($classSchedule as $schedule) {
              if (in_array($schedule->id, array_column($sessionItems, 'id'))) {
                $attended = $schedule->getAttendanceInfo($student->id);
                $studentSchedules[] = [
                  'schedule' => $schedule,
                  'attended' => $attended
                ];
              }
            }
            $data[] = [
              'student' => $student,
              'schedules' => $studentSchedules,
              'created_at'=> $studentInfo->created_at->format('Y-m-d'),
            ];
          }
        }

      }
      $date = date('Y-m-d');
      if(!empty($sessionItems[0])){
        $date = $sessionItems[0]->date;
      }
      $timestamp = strtotime($date);
      $firstDayOfWeek = strtotime('last sunday', $timestamp);

      for ($i = 1; $i <= 7; $i++) {
        $daysOfWeek[] = date('m/d', strtotime("+$i days", $firstDayOfWeek));
      }
      $perPage = 10;
      $currentPage = LengthAwarePaginator::resolveCurrentPage();
      $currentItems = collect($data)->slice(($currentPage - 1) * $perPage, $perPage)->all();
      $data = new LengthAwarePaginator($currentItems, count($data), $perPage);
      $data->withQueryString();
      $data->setPath($request->url());
      return view('admin.attendances.special.index',compact('classes','data','daysOfWeek','lastSession'));
    }

    public function editSpecial(Request $request,$id){
      if($request->class && $id){
        $student = $this->studentClassRepository->with(['student'])->findWhere(['student_id'=> $id,'class_id'=>$request->class])->first();
        if($student){
          return view('admin.attendances.special.edit',compact('student'));
        }
      }
      return redirect()->route('admin.attendance.regular');
    }

    public function storeSpecial(AttendeanceRequest $request){
      if($request->user_id && $request->class_id){
        $data = $this->studentClassRepository->with('classes')->findWhere(['student_id'=> $request->user_id,'class_id'=> $request->class_id])->first();
        if($data){
          $dataSchedule = $this->classScheduleRepository->findWhere(['class_id'=> $request->class_id,'date'=> $request->date])->first();
          if($dataSchedule){
            $dataAtt = $this->attendanceRepository->findWhere(['user_id'=> $request->user_id,'schedule_id'=> $dataSchedule->id])->first();
            if($dataAtt){
              $this->attendanceRepository->update(['status'=> $request->type],$dataAtt->id);
            }else{
              $this->attendanceRepository->create(['user_id'=> $request->user_id,'schedule_id'=> $dataSchedule->id,'status'=> $request->type]);
            }
            return redirect()->route('admin.attendance.special', ['class' => $request->class_id])->with('success','Updated successfully.');
          }
        }
      }
      return redirect()->back()->withInput()->with('error','The class does not exist');
    }

    public function private(Request $request){
      $classes = $this->classRepository->findWhere(['type'=>'Private','status'=>'active']);
      $data = [];

      if ($request->class) {
        $studentClassInfo = $this->studentClassRepository;
        if ($request->name) {
          $keyw = convertKeyword($request->name);
          $studentClassInfo->with(['student' => function ($query) use ($keyw) {
            $query->where(function ($query) use ($keyw) {
              $query->where('user_name','like', '%'.$keyw.'%')
                ->orWhere('name_english','like', '%'.$keyw.'%');
            });
          }]);
        }
        $studentClassInfo->with(['classes.schedule']);
        $studentClassInfo = $studentClassInfo->findWhere(['class_id' => $request->class]);
        foreach ($studentClassInfo as $studentInfo) {
          $student = $studentInfo->student;

          if($student && $studentInfo->classes){
            $classSchedule = $studentInfo->classes->schedule;
            $studentSchedules = [];
            foreach ($classSchedule as $schedule) {
              $attended = $schedule->getAttendanceInfo($student->id);
              $studentSchedules[] = [
                'schedule' => $schedule,
                'attended' => $attended
              ];
            }
            $data[] = [
              'student' => $student,
              'schedules' => $studentSchedules,
              'created_at'=> $studentInfo->created_at->format('Y-m-d'),
            ];
          }
        }

      }
      $date = date('Y-m-d');
      if($request->date){
        $date = $request->date;
      }
      if(!empty($sessionItems[0])){
        $date = $sessionItems[0]->date;
      }
      $timestamp = strtotime($date);
      $firstDayOfWeek = strtotime('last sunday', $timestamp);

      for ($i = 1; $i <= 7; $i++) {
        $daysOfWeek[] = date('m/d', strtotime("+$i days", $firstDayOfWeek));
      }
      $perPage = 10;
      $currentPage = LengthAwarePaginator::resolveCurrentPage();
      $currentItems = collect($data)->slice(($currentPage - 1) * $perPage, $perPage)->all();
      $data = new LengthAwarePaginator($currentItems, count($data), $perPage);
      $data->withQueryString();
      $data->setPath($request->url());
      return view('admin.attendances.private.index',compact('classes','data','daysOfWeek'));

    }

    public function editPrivate(Request $request,$id){
      if($request->class && $id){
        $student = $this->studentClassRepository->with(['student'])->findWhere(['student_id'=> $id,'class_id'=>$request->class])->first();
        if($student){
          return view('admin.attendances.private.edit',compact('student'));
        }
      }
      return redirect()->route('admin.attendance.private');
    }

    public function storePrivate(Request $request)
    {
        if ($request->type == 'participation') {
            Attendance::updateOrCreate(
                ['user_id' => $request->user_id, 'schedule_id' => $request->schedule_id],
                ['participation' => $request->status]
            );
            $this->updateGrade($request->all(),'participation');
            return response()->json(['status' => true]);
        }
        $dataAtt = $this->attendanceRepository->findWhere(['user_id' => $request->user_id, 'schedule_id' => $request->schedule_id])->first();
        $account = $this->accountRepository->find($request->user_id);
        $parent = $this->accountRepository->findWhere(['user_id' => $account->user_id, 'type' => 'parent'])->first();
        if ($dataAtt) {
            $this->attendanceRepository->update(['status' => $request->status ?: null], $dataAtt->id);
        } else {
            $this->attendanceRepository->create(['user_id' => $request->user_id, 'schedule_id' => $request->schedule_id, 'status' => $request->status ?: null]);
        }
        $this->updateGrade($request->all(),'attendance');
        if ($parent && $parent->uuid_kakao && $request->status && $request->status !== 'N/A') {
            $text = $request->status == 'X' || $request->status == 'Late' ? "({$account->user_name}) - 출석이 확인 되었습니다." : ($request->status == 'O' ? "({$account->user_name}) - 출석이 아직 확인 되지 않았습니다. 확인 부탁드립니다." : 'No Record');
            $payload = [
                'receiver_uuids' => [$parent->uuid_kakao],
                'template_object' => [
                    'object_type' => 'text',
                    'text' => $text,
                    'link' => [
                        'web_url' => 'https://example.com',
                        'mobile_web_url' => 'https://example.com',
                    ],
                ],
            ];
            SendKakaoMessageJob::dispatch($payload);
        }

        return response()->json(['status' => true]);
    }

    public function updateGrade($data,$type)
    {
        $class = ClassSchedule::with('classes')->find($data['schedule_id']);
        if($class->classes->type == 'Private'){
            $mainHomework = MainHomeworks::where('class', $class->classes->id)->where('schedule_id', $data['schedule_id'])->first();
        }else{
            $mainHomework = MainHomeworks::where('class', $class->classes->id)->where('session', $data['session'])->first();
        }
        if ($mainHomework) {
            $gradeHomework = GradeHomework::where('account_id', $data['user_id'])->where('main_homework_id', $mainHomework->id)->first();
            if ($gradeHomework) {
                $jsonScore = json_decode($gradeHomework->json_score, true);
                $jsonScore[$type] = $data['status'];
                $gradeHomework->update(['json_score' => json_encode($jsonScore)]);
            }else{
                GradeHomework::create([
                    'account_id' => $data['user_id'],
                    'main_homework_id' => $mainHomework->id,
                    'json_score' => json_encode(
                        [
                            $type => $data['status'],
                            'rangeTo' => '',
                            'rangeFrom' => '',
                            'scoreType' => 'range',
                            'grade' => '',
                            'percentage' => '',
                            'message' => '',
                        ]
                    ),
                    'status' => 'inactive',
                ]);
            }
        }
    }
}
