<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateExamRequest;
use App\Http\Resources\BasePaginatedCollection;
use App\Models\AccountHomework;
use App\Services\HomeworkService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class HomeworkController extends Controller
{
  public function __construct(protected  HomeworkService $homeworkService)
  {
  }

  public function store(CreateExamRequest $request)
  {
    $exam = $this->homeworkService->create($request->only([
      'title',
      'description',
      'homework',
      'type',
      'status',
      'main_homework_id',
    ]));
    if(isset($exam['error'])){
      return response()->json([
        'message' => $exam['error'],
      ],422);
    }

    Session::flash('success', 'Created successfully');
    return response()->json([
      'message' => 'Created successfully',
      'data' => $exam,
    ]);
  }

  public function edit($id)
  {
    $title = 'Edit';
    $homework = $this->homeworkService->getHomework($id);
    if (!$homework) {
      abort(404, 'Exam not found.');
    }
    return view('admin.exams.form', compact('title', 'id'));
  }

  public function show($id)
  {
    $homework = $this->homeworkService->getHomework($id);
    return response()->json([
      'data' => $homework
    ]);
  }

  public function update(Request $request, $id)
  {
    $this->homeworkService->update($request->only([
      'title',
      'description',
      'homework',
      'type',
      'status',
      'main_homework_id',
    ]), $id);
    return response()->json([
      'message' => 'Updated successfully',
    ]);
  }

  public function homeworkStudent(Request $request)
  {
      $data = $this->homeworkService->getHomeworkStudent($request->all());
      return response()->json(new BasePaginatedCollection($data));
  }

  public function homeworkStudentList(Request $request)
  {
      $data = $this->homeworkService->homeworkStudentList($request->all());
      return response()->json([
          'homeworks' => $data['homeworks'] ?? [],
          'main_homework' => $data ?? null,
      ]);
  }

  public function homeworkStudentDetail(Request $request)
  {
      $data = $this->homeworkService->homeworkStudentDetail($request);
      $mainHomework = $data['main_homework'];
      $homework = $data['data_homework'];
      if($homework){
          $homework->json_score = json_decode($homework->json_score);
      }
      $dataLinks = [];
      foreach ($mainHomework->homeworks as $item) {
          $dataLinks[] = [
              'id' => $item->id,
              'label' => isset($item->homework) ? 'HW' . $item->homework . '-' . $mainHomework->getClass->name :
                  $item->title . '-' . $mainHomework->getClass->name,
          ];
      }
      return response()->json([
          'data' => $data['tasks'] ?? [],
          'data_homework' => $homework?->json_score ?? null,
          'links' => $dataLinks,
      ]);
  }

  public function updateHomeworkStatus(Request $request)
  {
      $this->homeworkService->updateHomeworkStatus($request->all());
      return response()->json([
          'message' => 'Updated successfully',
      ]);
  }

  public function updateStatus(Request $request)
  {
    AccountHomework::where('id', $request->id)->update([
      'status' => $request->status,
    ]);
    return response()->json([
      'message' => 'Updated successfully',
    ]);
  }
}
