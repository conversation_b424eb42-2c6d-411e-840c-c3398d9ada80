<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UpcomingClassMail extends Mailable
{
    use Queueable, SerializesModels;

    public $className;
    public $scheduleStart;
    public $scheduleDate;
    public $classLink;
    public $studentName;

    public function __construct($className, $scheduleDate, $scheduleStart, $classLink, $studentName = null)
    {
        $this->className     = $className;
        $this->scheduleDate  = $scheduleDate;
        $this->scheduleStart = $scheduleStart;
        $this->classLink     = $classLink;
        $this->studentName   = $studentName;
    }

    public function build()
    {
        return $this->subject('Notification: Your class is about to start')
            ->view('emails.upcoming-class')
            ->with([
                'className'     => $this->className,
                'scheduleDate'  => $this->scheduleDate,
                'scheduleStart' => $this->scheduleStart,
                'classLink'     => $this->classLink,
                'studentName'   => $this->studentName,
            ]);
    }
}
