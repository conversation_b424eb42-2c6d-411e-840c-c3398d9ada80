<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CountryTimezone extends Model
{
    use HasFactory;

    protected $fillable = [
        'country_code',
        'country_name',
        'timezone',
    ];

    public function accounts()
    {
        return $this->hasMany(
            Account::class,
            'country_id',             
            'country_name'         
        );
    }

}
