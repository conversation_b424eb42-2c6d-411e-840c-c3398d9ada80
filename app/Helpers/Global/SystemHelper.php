<?php

use Carbon\Carbon;

if (!function_exists('convertKeyword')) {
  function convertKeyword($value, $char = '\\')
  {
    return str_replace(
      [$char, '%', '_'],
      [$char.$char, $char.'%', $char.'_'],
      $value
    );
  }
}
if (!function_exists('getSession')) {
    function getSession($schedules,$options = [])
    {
        $session = 1;
        $current_week_monday = null;
        $start_class = 0;
        $current_class = null;
        foreach ($schedules as $key => &$value) {
            if(!empty($value->session)){
                $session = $value->session;
                $value['session'] = $session;
                continue;
            }
            $term = $options['term'];
            $sunday = null;
            $sundayOption = null;
            $breakStart = null;
            $breakOptionStart = null;
            if (!empty($term)) {
                $breakStart = $term->break_start;
                $sunday = date("Y-m-d", strtotime("sunday this week", strtotime($term->break_end)));
                if ($term->break_option_start) {
                    $breakOptionStart = $term->break_option_start;
                    $sundayOption = date("Y-m-d", strtotime("sunday this week", strtotime($term->break_option_end)));
                }
            }
            if ($current_class && $current_class != $value->class_id) {
                $start_class = 0;
            } else {
                $start_class++;
            }
            if ($key == 0 || $start_class == 0) {
                $current_class = $value->class_id;
                $session = 1;
                $current_week_monday = date('Y-m-d', strtotime('monday this week', strtotime($value['date'])));
            }
            if ($breakStart) {
                if (date('Y-m-d', strtotime('monday this week', strtotime($value['date']))) !== $current_week_monday
                    && !($breakStart < $value['date'] && $sunday > $value['date'])) {
                    if ($breakOptionStart) {
                        if (!($breakOptionStart <= $value['date'] && $sundayOption >= $value['date'])) {
                            $session++;
                            $current_week_monday = date('Y-m-d', strtotime('monday this week', strtotime($value['date'])));
                        }
                    } else {
                        $session++;
                        $current_week_monday = date('Y-m-d', strtotime('monday this week', strtotime($value['date'])));
                    }
                }
                $value['session'] = $session;
            } else {
                $value['session'] = null;
            }
        }

        return $schedules;
    }
}

if (!function_exists('includeFilesInFolder')) {
    /**
     * Loops through a folder and requires all PHP files
     * Searches sub-directories as well.
     *
     * @param $folder
     */
    function includeFilesInFolder($folder)
    {
        try {
            $rdi = new RecursiveDirectoryIterator($folder);
            $it = new RecursiveIteratorIterator($rdi);

            while ($it->valid()) {
                if (!$it->isDot() && $it->isFile() && $it->isReadable() && $it->current()->getExtension() === 'php') {
                    require $it->key();
                }

                $it->next();
            }
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }
}

if (!function_exists('includeRouteFiles')) {

    /**
     * @param $folder
     */
    function includeRouteFiles($folder)
    {
        includeFilesInFolder($folder);
    }
}


if (!function_exists('truncate_string')) {

  function truncate_string($input,$number = 50)
  {
    $string = trim($input);

    if (mb_strlen($string) > $number) {
      return mb_substr($string, 0, $number);
    } else {
      return $string;
    }
  }
}

if (!function_exists('trimTextArea')) {
  function trimTextArea($input, $number = 50)
  {
    $trimmed = trim($input);
    $trimmed = mb_substr($trimmed, 0, $number);
    return $trimmed;
  }
}


if (!function_exists('gradeFormat')) {

  function gradeFormat($score) {
    if ($score >= 95) {
      return 'A+';
    } elseif ($score >= 90) {
      return 'A';
    } elseif ($score >= 80) {
      return 'A-';
    } elseif ($score >= 70) {
      return 'B+';
    } elseif ($score >= 60) {
      return 'B';
    } elseif ($score >= 50) {
      return 'B-';
    } elseif ($score >= 40) {
      return 'C+';
    } elseif ($score >= 30) {
      return 'C';
    } elseif ($score > 0) {
      return 'FM';
    } else {
      return 'N/A';
    }
  }
}

if (!function_exists('gradeFormatToPoint')) {
  function gradeFormatToPoint($score) {
    switch ($score) {
      case 'A+':
        return 97;
      case 'A':
        return 93;
      case 'A-':
        return 86;
      case 'B+':
        return 76;
      case 'B':
        return 66;
      case 'B-':
        return 56;
      case 'C+':
        return 47;
      case 'C':
        return 37;
      case 'Fp':
      case 'Fm':
        return 29;
      default:
        return 0;
    }
  }
}

if (!function_exists('isJson')) {
  function isJson($string) {
    json_decode($string);
    return json_last_error() === JSON_ERROR_NONE;
  }
}

if (!function_exists('formatCss')) {
    function formatCss($count): string
    {
        if($count == 1){
            return 'w-full';
        }
        return 'w-1/'.$count;
    }
}
