<?php

namespace App\Services;

use App\Enums\Constant;
use App\Enums\HomeworkType;
use App\Models\AccountHomework;
use App\Models\AccountHomeworkTask;
use App\Models\AccountTaskAnswer;
use App\Models\GradeHomework;
use App\Models\Homework;
use App\Models\MainHomeworks;
use App\Models\TaskHomework;
use App\Repositories\HomeworkRepository;
use App\Repositories\MainHomeWorksRepository;

class HomeworkService
{
  public function __construct(protected HomeworkRepository $homeworkRepo, protected MainHomeWorksRepository $mainHomeworkRepo)
  {

  }

  public function create($request)
  {
    $homeworkData = [
      'title' => $request['title'],
      'description' => $request['description'] ?? null,
      'status' => $request['status'] ?? Constant::INACTIVE,
    ];
    if($request['type'] == HomeworkType::CLASSWORK){
        $homeworkClassExist = Homework::where('main_homework_id',$request['main_homework_id'])
            ->where('type',HomeworkType::CLASSWORK)->exists();
        if($homeworkClassExist){
          return ['error' => 'Classwork already exist. You can only have one classwork per main homework.'];
        }
    }
    $homework = Homework::where('main_homework_id',$request['main_homework_id'])->where('type',HomeworkType::HOMEWORK)->count();
    $createData = array_merge($homeworkData, [
      'homework' => $request['homework'] ?? null,
      'type' => $request['type'],
      'order' => $request['type'] == HomeworkType::CLASSWORK ? 1 : $homework + 1,
      'main_homework_id' => $request['main_homework_id'],
    ]);

    return $this->homeworkRepo->create($createData);
  }

  public function getHomework($id)
  {
    return $this->homeworkRepo
        ->when(auth('admin')->user()->role == 'teacher', function ($query) {
            $query->whereHas('mainHomework.getClass.admins', function ($q) {
                $q->where('admin_id', auth('admin')->user()->id);
            });
        })
        ->where(['id' => $id])
      ->first();
  }

  public function update($request,$id)
  {
    $this->homeworkRepo->update($request,$id);
  }

  public function getHomeworkStudent($request)
  {
      return AccountHomework::with(['homework','mainHomework.getClass','account','accountHomeworkTasks'])
          ->when(!empty($request['main_homework_id']), function ($query) use ($request) {
              $query->whereHas('mainHomework', function ($query) use ($request) {
                  $query->where('id', $request['main_homework_id']);
              });
          })
          ->withSum('accountHomeworkTasks', 'score')
          ->withSum('accountHomeworkTasks', 'total_score')
          ->when(auth('admin')->user()->role == 'teacher', function ($query) {
              $query->whereHas('mainHomework.getClass.admins', function ($q) {
                  $q->where('admin_id', auth('admin')->user()->id);
              });
          })
          ->when(!empty($request['session']), function ($query) use ($request) {
              $query->whereHas('mainHomework', function ($query) use ($request) {
                  $query->where('session', $request['session']);
              });
          })
          ->when(!empty($request['class']), function ($query) use ($request) {
              $query->whereHas('mainHomework.getClass', function ($query) use ($request) {
                  $query->where('id', $request['class']);
              });
          })
          ->when(!empty($request['student']), function ($query) use ($request) {
              $query->where('account_id', $request['student']);
          })
          ->when(!empty($request['session_to']), function ($query) use ($request) {
              $query->whereHas('mainHomework', function ($query) use ($request) {
                  $query->where('session', '<=', $request['session_to']);
              });
          })
          ->when(!empty($request['session_from']), function ($query) use ($request) {
              $query->whereHas('mainHomework', function ($query) use ($request) {
                  $query->where('session', '>=', $request['session_from']);
              });
          })
          ->when(!empty($request['main_homework_id']), function ($query) use ($request) {
              $query->where('main_homework_id', $request['main_homework_id']);
          })
          ->orderBy('created_at', 'desc')
          ->paginate(10);
  }

  public function homeworkStudentList($request)
  {
      return MainHomeworks::where('class', $request['class'] ?? null)
          ->where('session', $request['session'] ?? null)
          ->with([
              'getClass',
              'gradeHomework' => function ($query) use ($request) {
                  $query->where('account_id', $request['student']);
              },
              'homeworks' => function ($query) {
                  $query->where('type',HomeworkType::HOMEWORK)->orderBy('id', 'desc');
              },
              'homeworks.accountHomework' => function ($query) use ($request) {
                  $query->where('account_id', $request['student']);
              },
          ])
          ->when(!empty($request['main_homework_id']), function ($query) use ($request) {
              $query->where('id', $request['main_homework_id']);
          })
          ->first();
  }

  public function homeworkStudentDetail($request)
  {
      $homework = Homework::where('id', $request['homework_id'])
      ->with([
          'mainHomework.homeworks' => function ($query) {
              $query->where('type',HomeworkType::HOMEWORK);
          },
          'mainHomework.getClass',
          'accountHomework' => function ($query) use ($request) {
              $query->where('account_id', $request['student_id']);
          },
          'accountHomework.accountHomeworkTasks.task',
          'accountHomework.accountHomeworkTasks.accountTaskAnswers' => function ($query) use ($request) {
              $query->where('account_id', $request['student_id']);
          },
          'accountHomework.accountHomeworkTasks.accountTaskAnswers.taskQuestion' => function ($query) {
              $query->whereColumn('task_id', 'task_questions.task_id');
          },
      ])->first();
      $mainHomework = $homework->mainHomework;
      $dataHomework = GradeHomework::where('account_id',$request['student_id'])
          ->where('main_homework_id',$mainHomework->id)->first();
      $dataReturn = [
          'data_homework' => $dataHomework,
          'tasks' => [],
          'main_homework' => $mainHomework,
      ];
      if(isset($homework->accountHomework->accountHomeworkTasks)) {
          foreach ($homework->accountHomework->accountHomeworkTasks as $task) {
              $score = 0;
              foreach ($task->accountTaskAnswers as $answer) {
                  $score += $answer->score ?? 0;
                  if ($answer->score_text) {
                      $task->score_text = $answer->score_text;
                  }
              }
              $task->total_score = $task->total_score ?? 0;
              $task->score = $score;
              $task->title = $task->task->title;
              $task->criteria = $task->task->criteria;
              $dataReturn['tasks'][] = $task;
          }
      }

      return $dataReturn;
  }

  public function updateHomeworkStatus($request)
  {
      $jsonScore = [
          'scoreType' => $request['scoreType'] ?? 'range',
          'rangeFrom' => $request['rangeFrom'] ?? null,
          'rangeTo' => $request['rangeTo'] ?? null,
          'percentage' => $request['percentage'] ?? null,
          'grade' => $request['grade'] ?? null,
          'attendance' => $request['attendance'] ?? null,
          'participation' => $request['participation'] ?? null,
          'message' => $request['message'] ?? null,
      ];
      $homework = Homework::find($request['homework_id']);
      GradeHomework::updateOrCreate([
          'account_id' => $request['student_id'],
          'main_homework_id' => $homework->main_homework_id,
      ],[
          'json_score' => json_encode($jsonScore),
          'status' => $request['status'] ?? 'inactive',
      ]);
  }

    public function saveTaskQuestion($data): void
    {
        $now = now();
        $accountId = $data['account_id'];
        $homeworkId = $data['homework_id'];
        $userHomeworkId = $data['user_homework_id'];
        $accountHomework = AccountHomework::find($userHomeworkId);
        if ($accountHomework) {
            $gradeHomework = GradeHomework::where('account_id', $accountId)
                ->where('main_homework_id', $accountHomework->main_homework_id)
                ->first();
            if(!$gradeHomework){
                GradeHomework::create([
                    'account_id' => $accountId,
                    'main_homework_id' => $accountHomework->main_homework_id,
                    'status' => 'inactive',
                    'json_score' => json_encode([
                        'rangeTo' => '',
                        'rangeFrom' => '',
                        'scoreType' => 'range',
                        'grade' => '',
                        'percentage' => '',
                        'attendance' => '',
                        'participation' => '',
                        'message' => '',
                    ]),
                ]);
            }
        }
        $tasks = TaskHomework::where('homework_id', $data['homework_id'])
            ->where('is_latest', 1)
            ->with('task.questions')
            ->orderBy('order')
            ->get();

        $existingHomeworkTasks = AccountHomeworkTask::where('account_id', $accountId)
            ->where('homework_id', $homeworkId)
            ->where('user_homework_id', $userHomeworkId)
            ->whereIn('task_id', $tasks->pluck('task_id'))
            ->pluck('task_id')
            ->toArray();

        $questionIds = [];
        foreach ($tasks as $task) {
            foreach ($task->task->questions as $question) {
                $questionIds[] = $question->id;
            }
        }

        $existingAnswers = AccountTaskAnswer::where('account_id', $accountId)
            ->whereIn('task_id', $tasks->pluck('task_id'))
            ->whereIn('question_id', $questionIds)
            ->get()
            ->groupBy(['task_id', 'question_id']);

        $homeworkTasksToInsert = [];
        $answersToInsert = [];
        $totalTaskScore = [];

        foreach ($tasks as $index => $task) {
            $totalTaskScore[$task->task_id] = 0;
            if (!in_array($task->task_id, $existingHomeworkTasks)) {
                $homeworkTasksToInsert[] = [
                    'account_id' => $accountId,
                    'homework_id' => $homeworkId,
                    'user_homework_id' => $userHomeworkId,
                    'task_id' => $task->task_id,
                    'task_unlock' => 0,
                    'order' => $index,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            foreach ($task->task->questions as $question) {
                $answerExists = isset($existingAnswers[$task->task_id][$question->id]);

                if (!$answerExists) {
                    $answersToInsert[] = [
                        'account_id' => $accountId,
                        'task_id' => $task->task_id,
                        'question_id' => $question->id,
                        'type' => $question->type,
                        'created_at' => $now,
                        'updated_at' => $now,
                        'total_score' => $question->pivot->score ?? 0,
                        'score' => 0,
                    ];
                }
                $totalTaskScore[$task->task_id] += $question->pivot->score ?? 0;
            }
        }

        if (!empty($homeworkTasksToInsert)) {
            AccountHomeworkTask::insert($homeworkTasksToInsert);
        }
        if (!empty($answersToInsert)) {
            AccountTaskAnswer::insert($answersToInsert);
        }
        foreach ($totalTaskScore as $taskId => $score) {
            AccountHomeworkTask::where('account_id', $accountId)
                ->where('homework_id', $homeworkId)
                ->where('task_id', $taskId)
                ->update(['total_score' => $score]);
        }
    }

}
