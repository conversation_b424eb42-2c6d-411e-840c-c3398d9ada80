import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Typography, Card } from '@mui/material'; // Import CircularProgress for loading indicator
import { fetchHomeworkTask, submitHomeworkAnswers, createRequestUnlock, checkStatusUnlockRequest } from '../../services/user/homework'; // Assuming you have startTaskExamSession API call

import TimeUpModal from './TimeUpModal';
import ExamInfoCard from './ExamInfoCard';
import ExamProgressCard from './ExamProgressCard';
import QuestionSection from './QuestionSection';
import QuestionNavigation from './QuestionNavigation';
import QuestionType from '../../enums/QuestionType';
import { getTaskTime } from '../utils/user/calculateTime';
import { processTaskQuestions } from '../utils/user/processTaskQuestions';
import { useForm, useWatch } from 'react-hook-form';
import { formatAnswersToSubmit } from '../utils/user/formatAnswersToSubmit';
import { UnlockIDText, TypeTask } from '../../enums/HomeworkType';
import getUnansweredQuestionNumbers from '../utils/user/getUnansweredQuestionNumbers ';
import { updateUserAnswerByType } from '../utils/user/updateUserAnswer';
import { debouncedSaveStorage, clearTaskFromStorage } from '../utils/user/taskStorage';
import { initTasksFromData } from '../utils/user/initTasks';
import { getListGrades } from '../../services/user/vocabulary-grades';

export default function ExamPage({
  initialTasks,
  initialHomeworkDetail,
  isResultPage = false,
  accountId,
  isGrades = false
}) {
  const defaultTimeLimit = 0;
  const initialHomeworkDetailState = {
    id: initialHomeworkDetail?.id || '',
    title: initialHomeworkDetail?.title || '',
    description: initialHomeworkDetail?.description || '',
    mainHomeworkId: initialHomeworkDetail?.main_homework_id || '',
    accountHomeworkId: initialHomeworkDetail?.user_homework_id || '',
  };
  const [grades, setGrades] = useState({});
  const tasksData = Array.isArray(initialTasks) ? initialTasks : [];
  const [homeworkDetail, setHomeworkDetail] = useState(initialHomeworkDetailState);
  const { homeworkId } = useParams();
  const timerId = useRef(null); // Ref to store timer ID for cleanup
  const userHomeworkId = initialHomeworkDetail?.user_homework_id || null;
  const [tasks, setTasks] = useState(() => initTasksFromData(tasksData, isResultPage, accountId));
  const [totalScoreHomework, totalTimeHomework] =
    tasks.length > 0 && isResultPage
      ? [
        tasks.reduce((sum, task) => sum + (task.totalScoreTask || 0), 0),
        tasks.reduce((sum, task) => sum + (task.timeLeft || 0), 0)
      ]
      : [0, 0];
  const indexOfFirstTask = (() => {
    // 1. Find the first task that has not started yet (startTime is null)
    const startednotEnIndex = tasks.findIndex(task => task.startTime !== null && task.end === null);
    if (startednotEnIndex !== -1) return startednotEnIndex;
    const notStartedIndex = tasks.findIndex(task => task.startTime === null && task.end === null);
    if (notStartedIndex !== -1) return notStartedIndex;
    return 0;
  })();
  const [currentTaskIndex, setCurrentTaskIndex] = useState(indexOfFirstTask);
  const currentTask = useMemo(() => tasks[currentTaskIndex], [tasks, currentTaskIndex]);

  const questions = currentTask?.questions || [];
  const timeLeft = currentTask?.timeLeft || 0;
  const examStarted = currentTask?.examStarted || false;
  const isLoading = currentTask?.isLoading || false;

  const [currentQuestion, setCurrentQuestion] = useState(indexOfFirstTask !== -1 ? indexOfFirstTask : 1); // Start at 1 if exam was started, otherwise 0
  const [correctCount, setCorrectCount] = useState(0);
  const [wrongCount, setWrongCount] = useState(0);
  const questionRefs = useRef({});
  const questionSectionRef = useRef(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [showConfirmSubmitDialog, setShowConfirmSubmitDialog] = useState(false);
  const [openTimeUpModal, setOpenTimeUpModal] = useState(false);
  const [showConfirmRequest, setShowConfirmRequest] = useState(false);
  const HEADER_HEIGHT = 70;
  const PAGE_PADDING_Y = 24;
  const totalQuestions = questions.length;
  const [textNotificationUnAnswered, setTextNotificationUnAnswered] = useState([]);

  useEffect(() => {
    const fetchGrades = async () => {
      const { data: { data } } = await getListGrades(homeworkId);
      const currentGrades = {}
      data.map(({ vocabulary_id, grade }) => {
        currentGrades[vocabulary_id] = grade;
      })
      setGrades(currentGrades);
    }

    homeworkId && fetchGrades();
  }, [homeworkId]);

  const {
    handleSubmit,
    control,
    formState: { errors }
  } = useForm({
    defaultValues: {
      tasks: initialTasks.map(task => ({
        ...task,
        task: {
          ...task.task,
          questions: task.task.questions.map(q => ({
            ...q,
            account_answers: {
              ...q.account_answers,
              score: q.account_answers?.type > 3 ? '' : q.account_answers?.score
            }
          }))
        }
      }))
    }
  });

  const questionsForm = useWatch({ name: 'tasks', control });

  const totalScore = useMemo(() => {
    return (
      questionsForm?.reduce((total, task) => {
        return (
          total +
          task.task.questions.reduce((taskTotal, q) => {
            return taskTotal + (parseFloat(q.account_answers?.score) || 0);
          }, 0)
        );
      }, 0) || 0
    );
  }, [questionsForm]);

  const setQuestionRef = useCallback((questionIndex, element) => {
    if (element) {
      questionRefs.current[questionIndex] = element;
    } else {
      delete questionRefs.current[questionIndex];
    }
  }, []);

  const formatTime = useCallback(seconds => {
    const m = Math.floor(seconds / 60)
      .toString()
      .padStart(2, '0');
    const s = (seconds % 60).toString().padStart(2, '0');
    return `${m} : ${s}`;
  }, []);

  const handleStartExam = useCallback(async () => {
    if (!currentTask) {
      setAlertMessage('No task available to start.');
      setShowAlert(true);
      return;
    }
    if (currentTask.examStarted) {
      console.log('Task already started.');
      return;
    }
    setCurrentQuestion(1);
    fetchExamData();
    setTasks(prevTasks =>
      prevTasks.map((task, idx) => {
        if (idx === currentTaskIndex) {
          return {
            ...task,
            examStarted: true,
            isLoading: false,
            startTime: new Date(),
            timeLeft: getTaskTime(new Date(), task.max_submit_time, task.endTime), // Optimistic initial time
            minTime: getTaskTime(new Date(), task.min_submit_time, task.endTime) // Optimistic initial time
          };
        }
        if (idx === currentTaskIndex + 1) {
          return {
            ...task,
            showUnlockTask: task.type !== TypeTask.NORMAL,
            showStart: false
          }; // Mark as submitted, stop timer
        }
        return task;
      })
    );
    try {
      console.log(`API call to start task ${currentTask.task_id} would go here.`);
    } catch (error) {
      console.error('Error starting task session on backend:', error.response?.data || error.message);
      setAlertMessage('Failed to officially start task session. Please try again.');
      setShowAlert(true);
      // Revert state if backend call fails
      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          if (idx === currentTaskIndex) {
            return {
              ...task,
              examStarted: false,
              isLoading: true,
              timeLeft: 0 // Revert to initial time
            };
          }
          return task;
        })
      );
    }
  }, [currentTaskIndex, defaultTimeLimit, homeworkId]);
  const fetchExamData = async () => {
    try {
      const response = await fetchHomeworkTask(homeworkId, currentTask.task_id, userHomeworkId);
      const data = response.data;
      const processedQuestions = processTaskQuestions(data.questions, isResultPage);
      console.log('Fetched questions:', processedQuestions);
    } catch (error) {
      console.error('Error fetching exam data:', error.response?.data || error.message);
      setAlertMessage('Error loading questions!');
      setShowAlert(true);
      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          if (idx === currentTaskIndex) {
            return { ...task, isLoading: false, questions: [] };
          }
          return task;
        })
      );
    }
  };

  const fetchCheckStatusUnlockRequest = async () => {
    try {
      const param = {account_id : accountId, task_id: currentTask.task_id}
      const response = await checkStatusUnlockRequest(param);
      const data = response?.data?.data;
      if(data == true){
        setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          if (idx === currentTaskIndex) {
            return { ...task, isLoading: false, showStart: true, showUnlockTask: false };
          }
          return task;
        })
      );
      }
      return;
    } catch (error) {
      console.error('Error fetching exam data:', error.response?.data || error.message);
      setAlertMessage('Error loading questions!');
      setShowAlert(true);
    }
  };
  // Effect for scrolling to current question
  useEffect(() => {
    if (
      examStarted &&
      !isLoading &&
      questions.length > 0 &&
      questionRefs.current[currentQuestion] &&
      questionSectionRef.current
    ) {
      const timeoutId = setTimeout(() => {
        const questionElement = questionRefs.current[currentQuestion];
        const container = questionSectionRef.current;
        const elementOffsetTop = questionElement.offsetTop;
        const targetScrollTop = elementOffsetTop - (HEADER_HEIGHT + PAGE_PADDING_Y + 20);
        container.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        });
      }, 50); // Small delay to ensure elements are rendered
      return () => clearTimeout(timeoutId);
    }
  }, [currentQuestion, isLoading, examStarted]);

  const startCountdown = taskIndex => {
    if (isResultPage) return; // Skip countdown in result page
    if (currentTask?.isCountDown === false) return; // Skip if countdown is not enabled
    if (currentTask?.isSubmitted === true) return; // Skip if countdown is not enabled
    if (timerId.current) clearInterval(timerId.current);
    const task = tasks[taskIndex];
    if (task?.timeLeft === 0 && task?.isSubmitted === false) {
      confirmAndSubmit(task.isSubmitted);
      clearInterval(timerId.current);
      setOpenTimeUpModal(true);
      return;
    }

    timerId.current = setInterval(() => {
      setTasks(prev => {
        return prev.map((task, idx) => {
          if (idx !== taskIndex) return task;

          const newTimeLeft = Math.max(0, task.timeLeft - 1)
          const newMinSubmitTime = Math.max(0, task.minTime - 1);
          return { ...task, timeLeft: newTimeLeft , minTime: task.minTime> 0? newMinSubmitTime : 0 };
        });
      });
    }, 1000);
  };
  // Effect for the countdown timer
  useEffect(() => {
    const task = tasks[currentTaskIndex];
    if (!task?.isCountDown || !examStarted || task.timeLeft < 0 || tasks[currentTaskIndex].isSubmitted == true) return;
    startCountdown(currentTaskIndex);

    return () => {
      if (timerId.current) {
        clearInterval(timerId.current);
        timerId.current = null;
      }
    };
  }, [examStarted, currentTaskIndex, tasks[currentTaskIndex]]);

  // Effect to update correct/wrong counts (still for the current task)
  useEffect(() => {
    let correct = 0;
    let wrong = 0;

    questions.forEach(q => {
      if (q.isCorrect === true) {
        correct++;
      } else if (q.isCorrect === false) {
        wrong++;
      }
    });

    setCorrectCount(correct);
    setWrongCount(wrong);
  }, []);

  const handleNextQuestion = useCallback(() => {
    if (currentQuestion < totalQuestions) {
      setCurrentQuestion(prev => prev + 1);
    }
  }, [currentQuestion, totalQuestions]);

  const handlePrevQuestion = useCallback(() => {
    if (currentQuestion > 1) {
      setCurrentQuestion(prev => prev - 1);
    }
  }, [currentQuestion]);

  const handleJumpToQuestion = useCallback(questionNum => {
    setCurrentQuestion(questionNum);
  }, []);
  const handleAnswerChange = useCallback(
    (questionId, value, indexOrIsChecked, typeOperation) => {
      setTasks(prevTasks =>
        prevTasks.map((task, taskIdx) => {
          if (taskIdx === currentTaskIndex) {
            // Prevent answer changes if task is submitted or time is up
            if (task.isSubmitted || task.timeLeft === 0) {
              return task; // Do not modify
            }
            const updatedQuestions = updateUserAnswerByType(
              task.questions,
              questionId,
              value,
              indexOrIsChecked,
              typeOperation
            );
            task.timeLeft = getTaskTime(task.startTime, task.maxSubmitTime)
            if (task?.min_submit_time && task.min_submit_time > 0) {
              task.minTime = task?.min_submit_time && task.startTime ? getTaskTime(task.startTime, task?.min_submit_time) : 0;
            }
            debouncedSaveStorage(accountId, homeworkId, task.task_id, updatedQuestions);
            return { ...task, questions: updatedQuestions };
          }
          return task;
        })
      );
    },
    [currentTaskIndex, accountId, homeworkId]
  );

  const calculateProgress = useCallback(() => {
    const answeredQuestions = questions.filter(q => {
      if (q.type === QuestionType.MULTIPLE_CHOICE || q.type === QuestionType.SINGLE_CHOICE) {
        return (
          Array.isArray(q.userAnswer) &&
          q.userAnswer.length > 0 &&
          q.userAnswer.some(ans => String(ans).trim() !== '')
        );
      } else if (q.type === QuestionType.MAKE_SENTENCE) {
        return (
          Array.isArray(q.userAnswer) && q.userAnswer.some(ans => ans !== null && String(ans).trim() !== '')
        );
      } else if (
        q.type === QuestionType.FILL_IN_THE_BLANK ||
        q.type === QuestionType.MAKE_PARAGRAPH ||
        q.type === QuestionType.ESSAY ||
        q.type === QuestionType.TRANSCRIPTION
      ) {
        return q.userAnswer !== null && String(q.userAnswer).trim() !== '';
      }
      return false;
    }).length;
    return answeredQuestions;
  }, [questions]);

  const handleNextTask = useCallback(() => {
    if (currentTaskIndex < tasks.length - 1) {
      let index = prev => prev + 1;
      setCurrentTaskIndex(index);
      setCurrentQuestion(1); // Reset question index when changing tasks
      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          return {
            ...task,
            timeLeft:
              task.startTime && !task.isSubmitted
                ? getTaskTime(task.startTime, task.maxSubmitTime)
                : task.timeLeft
          }; // Mark as submitted, stop timer
        })
      );
    }
  }, [currentTaskIndex, tasks.length]);
  const handlePrevTask = useCallback(() => {
    if (currentTaskIndex > 0) {
      let index = prev => prev - 1;
      setCurrentTaskIndex(index);
      setCurrentQuestion(1); // Reset question index when changing tasks

      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          return {
            ...task,
            timeLeft:
              task.startTime && !task.isSubmitted
                ? getTaskTime(task.startTime, task.maxSubmitTime)
                : task.timeLeft
          }; // Mark as submitted, stop timer
        })
      );
    }
  }, [currentTaskIndex]);

  const getUnratedVocabularies = useCallback((questions) => {
    return questions
      .filter(({ type }) => type === QuestionType.FLASH_CARD)
      .map(({ vocabularies, userAnswer }, index) => {
        const missingIds = vocabularies
          .filter(item => !(userAnswer && Object.prototype.hasOwnProperty.call(userAnswer, item.id.toString())))
          .map(item => item.id);

        return missingIds.length ? index + 1 : null;
      })
      .filter(index => index);

  }, []);
  const confirmAndSubmit = useCallback(async () => {
    setShowConfirmSubmitDialog(false);

    if (!currentTask || !currentTask.task_id) {
      setAlertMessage('Cannot submit: Task ID is missing.');
      setShowAlert(true);
      console.error('Submission failed: currentTask or task_id is undefined.');
      return;
    }
    if (currentTask?.isSubmitted == true) {
      return;
    }

    const taskInitialMaxTime = currentTask.max_submit_time || defaultTimeLimit;
    const timeSpentSeconds = taskInitialMaxTime - timeLeft;
    if (timeSpentSeconds < currentTask.min_submit_time) {
      console.log('timeSpentSeconds < currentTask.min_submit_time',timeSpentSeconds, currentTask.min_submit_time, taskInitialMaxTime , timeLeft);

      setAlertMessage(`You must spend at least ${formatTime(currentTask.min_submit_time)} on this task before submitting.`);
      setShowAlert(true);
      return;
    }
    const answersToSubmit = formatAnswersToSubmit(questions);

    try {
      const isLastTask =  tasks.filter(task => task.task_id !== currentTask.task_id)
      .every(task => task.isSubmitted === true);
      const payload = {
        answers: answersToSubmit,
        task_id: currentTask.task_id,
        homework_id: homeworkId,
        user_homework_id: userHomeworkId, // Pass userHomeworkId for backend processing
        is_last_task: isLastTask
      };
      const response = await submitHomeworkAnswers(payload);
      
      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          if (idx === currentTaskIndex) {
            return {
              ...task,
              isSubmitted: true,
              isCountDown: false,
              examStarted: true,
              endTime: new Date(),
              isLastTask: isLastTask,
            }; // Mark as submitted, stop timer
          }
          if (idx === currentTaskIndex + 1) {
            return {
              ...task,
              showUnlockTask: task.type === TypeTask.NORMAL ? false : true,
              showStart: task.type === TypeTask.NORMAL ? true : false
            }; // Mark as submitted, stop timer
          }
          return task;
        })
      );
      setAlertMessage('Answers submitted successfully!');
      setShowAlert(true);
      clearTaskFromStorage(accountId, homeworkId, currentTask.task_id);
      setCurrentQuestion(1);
    
    } catch (error) {
      console.error('Error submitting answers:', error.response?.data || error.message);

      let errorMessage = 'Failed to submit answers!';
      if (error.response && error.response.data && error.response.data.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setAlertMessage(errorMessage);
      setShowAlert(true);
    }
  }, [homeworkId, questions, timeLeft, defaultTimeLimit, currentTaskIndex, currentTask?.isSubmitted]);

  const handleSubmitHomeWork = useCallback(() => {
    setTextNotificationUnAnswered([])
    const unansweredQuestions = getUnansweredQuestionNumbers(questions);
    const unratedVocabularies = getUnratedVocabularies(questions);
    const invalidQuestions = [...unansweredQuestions, ...unratedVocabularies];
    if (invalidQuestions.length) {
      unansweredQuestions.length && setTextNotificationUnAnswered(prev => [...prev, `You haven't answered the following questions: ${unansweredQuestions.join(', ')}`]);
      unratedVocabularies.length && setTextNotificationUnAnswered(prev => [...prev, `You have not yet rated the flashcards for the following questions: ${unratedVocabularies.join(', ')}`]);
      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          if (idx === currentTaskIndex) {
            return { ...task, unansweredQuestions: invalidQuestions };
          }
          return task;
        })
      );
    }
    setShowConfirmSubmitDialog(true);
  }, [questions]);

  const sendRequestUnlockTask = async () => {
    setShowConfirmRequest(false);
    try {
      if (!currentTask || !currentTask.id) {
        setAlertMessage('Cannot request unlock: Task ID is missing.');
        setShowAlert(true);
        console.error('Request unlock failed: currentTask or task_id is undefined.');
        return;
      }
      await createRequestUnlock({
        account_id: accountId,
        unlock_type: UnlockIDText.TASK_UNLOCK,
        unlock_id: currentTask.task_id,
        main_homework_id: homeworkDetail?.mainHomeworkId,
        account_homework_id: homeworkDetail?.accountHomeworkId,
        homework_id: homeworkDetail?.id,
      });

      setTasks(prevTasks =>
        prevTasks.map((task, idx) => {
          if (idx === currentTaskIndex) {
            return { ...task, requestUnlocks: true };
          }
          return task;
        })
      );
      setAlertMessage('Task request sent successfully!');
    } catch (error) {
      setAlertMessage('Failed to send task request. Please try again.');
    }
  };
  const isTaskSubmitted = currentTask?.isSubmitted || false;
  const isTaskDisabled = timeLeft === 0 || !examStarted || currentTask?.isSubmitted;
  const onSubmitTask = useCallback(data => {
    console.log('hello world', data);
  });
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: isGrades ? `calc(100vh - 114px)` : '100vh',
        bgcolor: 'grey.100',
        overflow: 'hidden',
        maxHeight: isGrades ? `calc(100vh - 114px)` : '100vh'
      }}
    >
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          gap: 3,
          alignItems: 'flex-start',
          marginTop: isGrades ? 0 : `${HEADER_HEIGHT}px`,
          height: `calc(100vh - ${HEADER_HEIGHT}px)`,
          padding: `${PAGE_PADDING_Y}px`,
          boxSizing: 'border-box'
        }}
      >
        {/* Left Section: Exam Info and Progress */}
        <Box
          sx={{
            flexBasis: { xs: '100%', md: '25%' },
            flexGrow: 1,
            minWidth: { md: '280px' },
            position: 'sticky',
            top: isGrades ? 0 : `${HEADER_HEIGHT + PAGE_PADDING_Y}px`,
            height: '100%',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            gap: 3
          }}
        >
          <ExamInfoCard
            homeworkDetail={homeworkDetail}
            timeLeft={timeLeft}
            formatTime={formatTime}
            onSubmit={handleSubmitHomeWork}
            examStarted={examStarted}
            onStartExam={handleStartExam}
            isTaskSubmitted={isTaskSubmitted} // Pass this prop
            isResultPage={isResultPage}
            onRequestTask={() => setShowConfirmRequest(true)}
            currentTask={currentTask}
            totalScoreHomework={totalScoreHomework}
            totalTimeHomework={totalTimeHomework}
            isGrades={isGrades}
            totalScore={totalScore}
            onSubmitTask={handleSubmit(onSubmitTask)}
            checkStatusUnlockRequest={fetchCheckStatusUnlockRequest}
          />
          <ExamProgressCard
            tasks={tasks}
            correctCount={correctCount}
            wrongCount={wrongCount}
            answeredCount={calculateProgress()}
            totalQuestionsInPart={totalQuestions}
            currentTaskIndex={currentTaskIndex}
            totalTasks={tasks.length}
            onNextTask={handleNextTask}
            onPrevTask={handlePrevTask}
            currentTask={currentTask}
            isResultPage={isResultPage}
            formatTime={formatTime}
          />
          {currentTask?.examStarted && (
            <Box
              sx={{
                flexBasis: { xs: '100%', md: '20%' },
                flexGrow: 1,
                minWidth: { md: '280px' },
                position: 'sticky',
                top: `${HEADER_HEIGHT + PAGE_PADDING_Y}px`,
                height: '100%',
                overflowY: 'auto',
                display: 'flex',
                flexDirection: 'column',
                gap: 3
              }}
            >
              <QuestionNavigation
                totalQuestions={totalQuestions}
                currentQuestion={currentQuestion}
                onJumpToQuestion={handleJumpToQuestion}
                onNextQuestion={handleNextQuestion}
                onPrevQuestion={handlePrevQuestion}
                currentTaskIndex={currentTaskIndex}
                errors={errors}
                currentTask={currentTask}
              />
            </Box>
          )}
        </Box>

        {/* Middle Section: Question Section */}
        <Box
          ref={questionSectionRef}
          sx={{
            flexBasis: { xs: '100%', md: '75%' },
            flexGrow: 1,
            minWidth: { md: '450px' },
            height: 'calc(100dvh - 120px)',
            overflowY: 'auto',
            pr: 1,
            '&::-webkit-scrollbar': { display: 'none' },
            msOverflowStyle: 'none',
            scrollbarWidth: 'none'
          }}
        >
          {!examStarted && !isLoading ? ( // Show start message if exam hasn't started and not loading
            <Card elevation={1} sx={{ p: 4, textAlign: 'center' }}>
              <Typography sx={{ fontSize: '1rem' }} color='text.secondary' fontWeight={'bold'}>
               {currentTask?.requestUnlocks && !currentTask?.showStart
                ? "Please reload to check the unlock status."
                :!currentTask?.showStart
                  ? 'Please send a request to unlock the task to begin.'
                  : `Press 'Start task' to begin the task.`}
              </Typography>
              <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                {!currentTask?.showStart ? '' : ` Once started, the timer will begin.`}
              </Typography>
              {currentTask?.description && (
                <Typography variant='body2' color='text.secondary' sx={{ mt: 1 }}>
                  Description task: {currentTask?.description ?? ''}
                </Typography>
              )}

            </Card>
          ) : (
            <QuestionSection
              questions={questions}
              setQuestionRef={setQuestionRef}
              currentQuestion={currentQuestion}
              onAnswerChange={handleAnswerChange}
              loading={isLoading} // This will be false here, as we show spinner above
              disabled={isTaskDisabled}
              isResultPage={isResultPage}
              currentTask={currentTask}
              control={control}
              currentTaskIndex={currentTaskIndex}
              isGrades={isGrades}
              grades={grades}
            />
          )}
        </Box>
      </Box>

      {/* Alert Dialog */}
      <Dialog
        open={showAlert}
        onClose={() => {
          setShowAlert(false)
          if (currentTask.isLastTask) {
            window.location.href = (`/homeworks-result/${initialHomeworkDetail.user_homework_id}?accountId=${accountId}`);
          }
        }}
        aria-labelledby='alert-dialog-title'
        aria-describedby='alert-dialog-description'
      >
        <DialogTitle id='alert-dialog-title'>Notification</DialogTitle>
        <DialogContent>
          <Typography id='alert-dialog-description'>{alertMessage}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setShowAlert(false)
            if (currentTask.isLastTask && currentTask.isSubmitted == true) {
              window.location.href = (`/homeworks-result/${initialHomeworkDetail.user_homework_id}?accountId=${accountId}`);
            }
            }} autoFocus>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={showConfirmSubmitDialog}
        onClose={() => setShowConfirmSubmitDialog(false)}
        aria-labelledby='confirm-submit-dialog-title'
        aria-describedby='confirm-submit-dialog-description'
      >
        <DialogTitle id='confirm-submit-dialog-title'>Confirm Submission</DialogTitle>
        <DialogContent>
          {textNotificationUnAnswered.map((text, index) => (
            <Typography key={index} fontWeight='bold' id='confirm-submit-dialog-description'>
              {text}
            </Typography>
          ))}
          <Typography id='confirm-submit-dialog-description'>
            Are you sure you want to submit your answers? You will not be able to edit them after
            submission.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirmSubmitDialog(false)} color='primary'>
            Cancel
          </Button>
          <Button onClick={confirmAndSubmit} color='primary' autoFocus>
            Submit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={showConfirmRequest}
        onClose={() => setShowConfirmRequest(false)}
        aria-labelledby='confirm-start-task-dialog-title'
        aria-describedby='confirm-start-task-dialog-description'
      >
        <DialogTitle id='confirm-start-task-dialog-title'>Confirm Task Request</DialogTitle>
        <DialogContent>
          <Typography id='confirm-start-task-dialog-description'>
            Are you sure you want to request this task? Once started, you will need to complete it within
            the given time limit.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfirmRequest(false)} color='primary'>
            Cancel
          </Button>
          <Button onClick={sendRequestUnlockTask} color='primary' autoFocus>
            Send
          </Button>
        </DialogActions>
      </Dialog>

      {examStarted && timeLeft === 0 && !isTaskSubmitted && (
        <TimeUpModal open={openTimeUpModal} onClose={() => setOpenTimeUpModal(false)} />
      )}
    </Box>
  );
}
