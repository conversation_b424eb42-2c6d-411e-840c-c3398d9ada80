// src/components/StudentHomeworkAction.js

import React from 'react';
import {Button, IconButton, Tooltip} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CountdownButton from './CountdownButton';
import { Role, TypeHomework } from '../../../../enums/HomeworkType';
import AutorenewIcon from "@mui/icons-material/Autorenew";

export default function HomeworkAction({
    hw,
    role,
    handleOpenConfirmDialog,
    handleOpenConfirmUnlockDialog,
    fetchData,
    accountId,
    isResult,
    handleAutotUnlock,
    main,
    type=TypeHomework.HOMEWORK,
    schedules
}) {
    const accountHWByAccountPrew = hw.prevHwAccountHomeworkArray.find(
        ah =>
            ah.homework_id === hw?.prevHw?.id &&
            ah.account_id === accountId &&
            ah.main_homework_id === hw.mainHomeworkId
    );
    const accountHW = hw?.account_homeworks?.find(
        ah => ah.homework_id === hw.id && ah.account_id === accountId && ah.main_homework_id === hw.mainHomeworkId
    );

    const hwUnlockRequest = hw?.unlock_request.find(
        un_rq => un_rq.unlock_id === hw.id && un_rq.account_id === accountId
    );
    const showLoadingUnlock = hwUnlockRequest && hwUnlockRequest?.status  == 1;
    const prevHwEndTime = accountHWByAccountPrew?.end_time;
    const prevHwStartTime = accountHWByAccountPrew?.start_time;
    let unlockCountdownEndTime = null;
    let showUnlockRequest =
        (prevHwStartTime && !showLoadingUnlock) ||
        (hw.prevHwIsFirst && !hwUnlockRequest && role === Role.PARENT)
        || (hw.prevHwIsFirst && role === Role.STUDENT)
            ? true
            : false;
    const accountMainHomeworkId = accountHWByAccountPrew?.account_main_homework_id;
    const status = function getStatus(schedules, main, accountHW) {
      const now = new Date();
      const startTime = new Date(schedules?.[main.class]?.startTime);
      const endTime = new Date(schedules?.[main.class]?.endTime);
      return ((!schedules?.[main.class]?.startTime || !schedules?.[main.class]?.endTime) || (now >= startTime && now <= endTime))
        && (!accountHW?.homework_unlock || accountHW?.status === 1) && schedules?.[main.class]?.type === 'Regular';
    }
    if (prevHwEndTime  && !isResult && !status(schedules, main, accountHW)) {
        const prevEndTimeObj = new Date(prevHwEndTime);
        unlockCountdownEndTime = new Date(prevEndTimeObj.getTime() + 24 * 60 * 60 * 1000);
        let checkUnlock = false;
        let now = new Date();
        if (now >= unlockCountdownEndTime.getTime() && !accountHW?.homework_unlock) {
            // ⚠️ Auto call unlock nếu chưa unlock
            handleAutotUnlock(main.id, hw.id, accountId, accountMainHomeworkId);
            checkUnlock = true;
            showUnlockRequest = true;
        }
    }
    const link = isResult
        ? `/homeworks-result/${accountHW?.id}?accountId=${accountId}`
        : `/homeworks/${hw.id}?classwork=${type === TypeHomework.CLASSWORK}&accountId=${accountId}`;
    const isParent = role === Role.PARENT;
    if (hw.isFirst) {
        if (accountHW?.end_time) {
            return (
                <Tooltip title='View'>
                    <IconButton onClick={isParent && !isResult? undefined : () => (window.location.href = link)} color='success'>
                        <VisibilityIcon />
                    </IconButton>
                </Tooltip>
            );
        } else {
          if (status(schedules, main, accountHW)) {
              return (
                <CountdownButton
                  endTime={null}
                  onUnlock={() => handleOpenConfirmUnlockDialog(hw.id, hw.title, accountId, main.id)}
                  onAutoUnlock={fetchData}
                  hasAutoUnlocked={hwUnlockRequest}
                  role={role}
                  prevHwEndTime={prevHwEndTime}
                  showUnlockRequest={true}
                  showLoadingUnlock={showLoadingUnlock}
                  hiddenTime={true}
                />
              );
          }
          return (
            <Tooltip title='Start homework'>
              <IconButton
                onClick={
                  !isParent
                    ? () =>
                      !isResult
                        ? handleOpenConfirmDialog(hw.id, hw.title)
                        : (window.location.href = link)
                    : undefined
                }
                color='primary'
              >
                <AssignmentIcon />
              </IconButton>
            </Tooltip>
          );

        }
    }

    if (accountHW?.end_time) {
        return (
            <Tooltip title='View'>
                <IconButton  onClick={isParent && !isResult? undefined : () => (window.location.href = link)} color='success'>
                    <VisibilityIcon />
                </IconButton>
            </Tooltip>
        );
    }

    if (accountHW?.homework_unlock ||type === TypeHomework.CLASSWORK) {
      if (type === TypeHomework.CLASSWORK) {
          return (
            <Tooltip title='Start homework'>
              <IconButton
                onClick={
                  !isParent
                    ? () =>
                      !isResult ? handleOpenConfirmDialog(hw.id, hw.title) : (window.location.href = link)
                    : undefined
                }
                color='primary'
              >
                <AssignmentIcon />
              </IconButton>
            </Tooltip>
          );
      }

      if (status(schedules, main, accountHW)) {
        return (
          <CountdownButton
            endTime={null}
            onUnlock={() => handleOpenConfirmUnlockDialog(hw.id, hw.title, accountId, main.id)}
            onAutoUnlock={fetchData}
            hasAutoUnlocked={hwUnlockRequest}
            role={role}
            prevHwEndTime={prevHwEndTime}
            showUnlockRequest={true}
            showLoadingUnlock={showLoadingUnlock}
            hiddenTime={true}
          />
        );
      }
      return (
        <Tooltip title='Start homework'>
          <IconButton
            onClick={
              !isParent
                ? () =>
                  !isResult ? handleOpenConfirmDialog(hw.id, hw.title) : (window.location.href = link)
                : undefined
            }
            color='primary'
          >
            <AssignmentIcon />
          </IconButton>
        </Tooltip>
      );
    }

    if (showUnlockRequest || showLoadingUnlock || prevHwEndTime) {
        return (
            <CountdownButton
                endTime={unlockCountdownEndTime ? new Date(unlockCountdownEndTime) : null}
                onUnlock={() => handleOpenConfirmUnlockDialog(hw.id, hw.title, accountId, main.id)}
                onAutoUnlock={fetchData}
                hasAutoUnlocked={hwUnlockRequest}
                role={role}
                prevHwEndTime={prevHwEndTime}
                showUnlockRequest={showUnlockRequest}
                showLoadingUnlock={showLoadingUnlock}
                hiddenTime={status(schedules, main, accountHW)}
            />
        );
    }

    return (
        <Tooltip title='Homework not opened'>
            <IconButton disabled>
                <AssignmentIcon />
            </IconButton>
        </Tooltip>
    );
}
