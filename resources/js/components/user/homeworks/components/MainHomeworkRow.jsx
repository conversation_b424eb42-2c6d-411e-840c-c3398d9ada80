// MainHomeworkRow.jsx
import React from 'react';
import {
    Box,
    Collapse,
    IconButton,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography
} from '@mui/material';
import { KeyboardArrowUp, KeyboardArrowDown } from '@mui/icons-material';
import { HomeworkType, Role } from '../../../../enums/HomeworkType';
import HomeworkAction from './HomeworkAction';
import StudentStatusTable from './StudentStatusTable';
import { TableCellCustom, TypographyCustom } from '../../../../pages/admin/components/TableList';

export default function MainHomeworkRow({
    main,
    openRows,
    openHomeworks,
    toggleHomeworkRow,
    role,
    handleOpenConfirmDialog,
    handleOpenConfirmUnlockDialog,
    fetchData,
    isParent,
    accountId,
    isResult = false,
    handleAutotUnlock,
    type,
    schedules
}) {
    return (
        //
        <>
            <TableRow>
                <TableCellCustom
                    colSpan={6}
                    sx={{
                        p: 0,
                        borderBottom: 0,
                        width: '100%',
                        display: 'table-cell'
                    }}
                >
                    <Collapse
                        in={openRows[main.id]}
                        timeout='auto'
                        unmountOnExit
                        sx={{
                            width: '100%',
                            '& .MuiCollapse-wrapper': {
                                width: '100%'
                            },
                            '& .MuiCollapse-wrapperInner': {
                                width: '100%'
                            }
                        }}
                    >
                        {main.homeworks.length > 0 ? (
                            // <TableRow sx={{ width: '100%' }}>
                            //     <TableCellCustom
                            //         colSpan={6}
                            //         sx={{
                            //             p: 0,
                            //             borderBottom: 0,
                            //             backgroundColor: 'grey.50'
                            //         }}
                            //     >
                            <Box
                                sx={{
                                    ml: 4,
                                    mt: 2,
                                    mb: 2,
                                    borderLeft: '2px solid',
                                    borderColor: 'green',
                                    pl: 3
                                }}
                            >
                                <>
                                    <TableContainer
                                        component={Paper}
                                        elevation={0}
                                        sx={{ border: '1px solid', borderColor: 'divider' }}
                                    >
                                        <Table sx={{ tableLayout: 'fixed' }}>
                                            <TableHead>
                                                <TableRow>
                                                    <TableCell sx={{ width: '5%' }}></TableCell>
                                                    <TableCell
                                                        sx={{
                                                            fontWeight: 'bold',
                                                            backgroundColor: '#fff',
                                                            width: '10%'
                                                        }}
                                                    >
                                                        ID
                                                    </TableCell>
                                                    <TableCell
                                                        sx={{
                                                            fontWeight: 'bold',
                                                            backgroundColor: '#fff',
                                                            width: '15%'
                                                        }}
                                                    >
                                                        Title
                                                    </TableCell>
                                                    <TableCell
                                                        sx={{
                                                            fontWeight: 'bold',
                                                            backgroundColor: '#fff',
                                                            width: '35%'
                                                        }}
                                                    >
                                                        Description
                                                    </TableCell>
                                                    <TableCell
                                                        align='center'
                                                        sx={{
                                                            fontWeight: 'bold',
                                                            backgroundColor: '#fff',
                                                            width: '35%'
                                                        }}
                                                    >
                                                        Action
                                                    </TableCell>
                                                </TableRow>
                                            </TableHead>
                                            <TableBody>
                                                {main.homeworks.map((hw, idx) => {
                                                    return (
                                                        <React.Fragment key={hw.id}>
                                                            <TableRow hover sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                                                                {/* <TableCellCustom> */}
                                                                {isParent && (
                                                                    <TableCellCustom sx={{ width: '5%' }}>
                                                                        {isParent && (
                                                                            <IconButton
                                                                                size='small'
                                                                                onClick={() => toggleHomeworkRow(hw.id)}
                                                                            >
                                                                                {openHomeworks[hw.id] ? (
                                                                                    <KeyboardArrowUp />
                                                                                ) : (
                                                                                    <KeyboardArrowDown />
                                                                                )}
                                                                            </IconButton>
                                                                        )}
                                                                    </TableCellCustom>
                                                                )}
                                                                {!isParent && <TableCellCustom sx={{ width: '5%' }} />}
                                                                <TableCellCustom>
                                                                    <TypographyCustom
                                                                        variant='body1'
                                                                        fontWeight='medium'
                                                                    >
                                                                      {`${type === HomeworkType.CLASSWORK ? "CW" : "HW"}${hw?.homework ?? idx + 1}`}
                                                                    </TypographyCustom>
                                                                </TableCellCustom>
                                                                <TableCellCustom>
                                                                    <TypographyCustom
                                                                        variant='body1'
                                                                        fontWeight='medium'
                                                                    >
                                                                        {hw.title}
                                                                    </TypographyCustom>
                                                                </TableCellCustom>
                                                                <TableCellCustom>
                                                                    <TypographyCustom
                                                                        variant='body1'
                                                                        fontWeight='medium'
                                                                    >
                                                                        {hw.description}
                                                                    </TypographyCustom>
                                                                </TableCellCustom>

                                                                {role === Role.STUDENT && (
                                                                    <TableCellCustom
                                                                        align='center'
                                                                        sx={{ width: '15%' }}
                                                                    >
                                                                        <HomeworkAction
                                                                            hw={hw}
                                                                            role={role}
                                                                            handleOpenConfirmDialog={
                                                                                handleOpenConfirmDialog
                                                                            }
                                                                            handleOpenConfirmUnlockDialog={
                                                                                handleOpenConfirmUnlockDialog
                                                                            }
                                                                            fetchData={fetchData}
                                                                            accountId={accountId}
                                                                            isResult={isResult}
                                                                            handleAutotUnlock={handleAutotUnlock}
                                                                            main={main}
                                                                            type={type}
                                                                            schedules={schedules}
                                                                        />
                                                                    </TableCellCustom>
                                                                )}
                                                                {isParent && (
                                                                    <TableCellCustom
                                                                        align='center'
                                                                        sx={{ width: '15%' }}
                                                                    />
                                                                )}
                                                            </TableRow>
                                                          <TableRow>
                                                            <TableCell colSpan={6} sx={{ p: 0, border: 0 }}>
                                                              <Collapse in={openHomeworks[hw.id]} timeout='auto' unmountOnExit>
                                                                <Box sx={{ margin: 1 }}>
                                                                  <StudentStatusTable
                                                                    homeworks={[hw]}
                                                                    students={main?.get_class?.students}
                                                                    handleOpenConfirmDialog={handleOpenConfirmDialog}
                                                                    handleOpenConfirmUnlockDialog={
                                                                      handleOpenConfirmUnlockDialog
                                                                    }
                                                                    fetchData={fetchData}
                                                                    role={role}
                                                                    handleAutotUnlock={handleAutotUnlock}
                                                                    isResult={isResult}
                                                                    main={main}
                                                                  />
                                                                </Box>
                                                              </Collapse>
                                                            </TableCell>
                                                          </TableRow>
                                                        </React.Fragment>
                                                    );
                                                })}
                                            </TableBody>
                                        </Table>
                                    </TableContainer>
                                </>
                            </Box>
                        ) : (
                            <Typography sx={{ p: 2 }} align='center'>
                                No results found.
                            </Typography>
                        )}
                    </Collapse>
                </TableCellCustom>
            </TableRow>
        </>
    );
}
