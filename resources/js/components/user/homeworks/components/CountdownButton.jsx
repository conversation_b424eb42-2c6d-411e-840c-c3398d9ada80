import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>ack } from '@mui/material';
import LockIcon from '@mui/icons-material/Lock';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import { Role } from '../../../../enums/HomeworkType';

export default function CountdownButton({
    endTime,
    onUnlock,
    onAutoUnlock,
    hasAutoUnlocked,
    role,
    prevHwEndTime,
    showUnlockRequest,
    showLoadingUnlock,
    hiddenTime
}) {
    const [secondsLeft, setSecondsLeft] = useState(0);

    useEffect(() => {
        if (!endTime) return;

        const endTimestamp = new Date(endTime).getTime();

        const updateCountdown = () => {
            const now = Date.now();
            const diff = Math.max(0, Math.floor((endTimestamp - now) / 1000));
            setSecondsLeft(diff);

            if (diff <= 0 && !hasAutoUnlocked) {
                if (onAutoUnlock) onAutoUnlock();
            }
        };

        updateCountdown();
        const interval = setInterval(updateCountdown, 1000);

        return () => clearInterval(interval);
    }, [endTime, onAutoUnlock, hasAutoUnlocked]);

    const formatTime = totalSeconds => {
        const h = String(Math.floor(totalSeconds / 3600)).padStart(2, '0');
        const m = String(Math.floor((totalSeconds % 3600) / 60)).padStart(2, '0');
        const s = String(totalSeconds % 60).padStart(2, '0');
        return `${h}:${m}:${s}`;
    };

    const handleReload = () => {
        window.location.reload();
    };

    return (
        <Stack direction='row' spacing={2} justifyContent='center'>
            {prevHwEndTime && !hiddenTime && (
                <Button variant='outlined' color='warning' disabled size='small' sx={{ fontSize: '0.75rem' }} >
                    {formatTime(secondsLeft)}
                </Button>
            )}
            {showLoadingUnlock ? (
                <>
                    <Button variant='outlined' color='secondary' disabled sx={{ fontSize: '0.75rem' }} size='small'>
                        Requesting...
                    </Button>
                    <Button
                        variant='contained'
												size='small'
                        color='primary'
                        sx={{ fontSize: '0.75rem' }}
                        onClick={handleReload}
                        startIcon={<AutorenewIcon />}
                    />
                </>
            ) : (
                <>
                    {showUnlockRequest && (
                        <Button
                            variant='contained'
                            size='small'
                            sx={{ fontSize: '0.75rem' }}
                            color='error'
                            onClick={onUnlock}
                            startIcon={<LockIcon />}
                        >
                            Unlock
                        </Button>
                    )}
                </>
            )}
        </Stack>
    );
}
