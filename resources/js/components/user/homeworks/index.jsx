import React, { useState, useEffect } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Typography,
    TextField,
    IconButton,
    Box,
    Tooltip,
    Collapse,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Skeleton
} from '@mui/material';
import {
    KeyboardArrowDown,
    KeyboardArrowUp,
    Edit as EditIcon,
    Visibility as VisibilityIcon
} from '@mui/icons-material';
import axios from 'axios';
import { Pagination } from '@mui/material';
import AssignmentIcon from '@mui/icons-material/Assignment';
import ConfirmDialog from '../../ConfirmDialog';
import {
    createRequestUnlock,
    fetchMainClasswork,
    fetchMainHomeworks,
    submitAutoUnlock
} from '../../../services/user/homework';
import { HomeworkType, Role, TypeHomework, UnlockIDText } from '../../../enums/HomeworkType';
import { transformedHomeworkData } from '../../utils/user/transformedHomeworkData';
import MainHomeworkRow from './components/MainHomeworkRow';
import { TableCellCustom, TypographyCustom } from '../../../pages/admin/components/TableList';
import { useSearchParams } from 'react-router-dom';

export default function UserHomeworks({ type }) {
    const [data, setData] = useState([]);
    const [schedules, setSchedules] = useState([]);

    const [searchParams, setSearchParams] = useSearchParams();
    const pageParam = parseInt(searchParams.get("page") || "1", 10);
    const rowsPerPageParam = parseInt(10);
    const searchInputParam = searchParams.get("search") || "";

    const [search, setSearch] = useState(searchInputParam);
    const [page, setPage] = useState(pageParam);
    const [perPage] = useState(rowsPerPageParam);

    const [openRows, setOpenRows] = useState({});
    const [loading, setLoading] = useState(false);
    const [accountId, setAccountId] = useState(null);
    const [meta, setMeta] = useState({ current_page: 1, last_page: 1, total: 0 });
    const [alertMessage, setAlertMessage] = useState('');
    const [showAlert, setShowAlert] = useState(false);
    const [role, setRole] = useState(null);
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [openConfirmUnlockDialog, setOpenConfirmUnlockDialog] = useState(false);
    const [selectedHomeworkId, setSelectedHomeworkId] = useState(null);
    const [studentId, setStudentId] = useState(null);
    const [mainHomeworkId, setMainHomeworkId] = useState(null);
    // New state to store the homework title
    const [selectedHomeworkTitle, setSelectedHomeworkTitle] = useState('');
    const [openHomeworks, setOpenHomeworks] = useState({});
    const toggleHomeworkRow = id => {
        setOpenHomeworks(prev => ({ ...prev, [id]: !prev[id] }));
    };
    const fetchData = async (prop) => {
        try {
            setLoading(true);
            const newParam = {
                search: prop?.search ?? search,
                page: prop?.page ?? page,
            }
            setSearchParams(newParam);
            let res = null;
            const param = { ...newParam, per_page: perPage };
            if (type === TypeHomework.HOMEWORK) {
                res = await fetchMainHomeworks(param);
            }
            if (type === TypeHomework.CLASSWORK) {
                res = await fetchMainClasswork(param);
            }
            const { data, meta: paginationMeta, accountId, role, schedules } = res?.data;
            if (data.length >= 0) {
                const transformed = transformedHomeworkData(data, handleAutotUnlock, role);
                setData(transformed);
                setSchedules(schedules);
                setMeta(paginationMeta);
                setAccountId(accountId);
                setLoading(false);
                setRole(role);
            }
        } catch (e) {
            console.error('Error fetching homeworks:', e);
            setData([]);
            setMeta({ current_page: 1, last_page: 1, total: 0 });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (loading) return;
        fetchData();
    }, [page]);


    const toggleRow = id => {
        setOpenRows(prev => ({ ...prev, [id]: !prev[id] }));
    };

    // Modified handleOpenConfirmDialog to accept title as well
    const handleOpenConfirmDialog = (homeworkId, homeworkTitle) => {
        setSelectedHomeworkId(homeworkId);
        setSelectedHomeworkTitle(homeworkTitle); // Store the title
        setOpenConfirmDialog(true);
    };

    const handleCloseConfirmDialog = () => {
        setOpenConfirmDialog(false);
        setSelectedHomeworkId(null);
        setSelectedHomeworkTitle(''); // Clear the title on close
    };

    const handleConfirmStartHomework = () => {
        if (selectedHomeworkId) {
            if(type == TypeHomework.CLASSWORK){
                window.location.href = `/homeworks/${selectedHomeworkId}?classwork=true`;
            }else{
                window.location.href = `/homeworks/${selectedHomeworkId}`;
            }
        }
        handleCloseConfirmDialog();
    };
    const handleCloseConfirmUnlockDialog = () => {
        setOpenConfirmUnlockDialog(false);
        setSelectedHomeworkId(null);
        setSelectedHomeworkTitle(''); // Clear the title on close
    };

    const handleOpenConfirmUnlockDialog = (homeworkId, homeworkTitle, idStudent, mainHomeworkId) => {
        setSelectedHomeworkId(homeworkId);
        setSelectedHomeworkTitle(homeworkTitle); // Store the title
        setOpenConfirmUnlockDialog(true);
        setStudentId(idStudent);
        setMainHomeworkId(mainHomeworkId);
    };
    const handleRequestUnlock = async () => {
        try {
            if (!selectedHomeworkId) {
                setAlertMessage('Cannot request unlock: homework ID is missing.');
                setShowAlert(true);
                console.error('Request unlock failed: homeworkId or homework id is undefined.');
                return;
            }
            const response = await createRequestUnlock({
                account_id: studentId,
                unlock_type: UnlockIDText.HOMEWORK_UNLOCK,
                unlock_id: selectedHomeworkId,
                main_homework_id: mainHomeworkId
            });
            handleCloseConfirmUnlockDialog();
            setAlertMessage('Homework unlock request sent successfully!');
            setShowAlert(true);
            fetchData();
            setSelectedHomeworkId(null);
            setSelectedHomeworkTitle(null); // Store the title
            setOpenConfirmUnlockDialog(null);
            setStudentId(null);
            setMainHomeworkId(null);
        } catch (error) {
            setAlertMessage('Failed to send homework unlock request. Please try again.');
            setShowAlert(true);
            handleCloseConfirmUnlockDialog();
        }
    };

    const handleAutotUnlock = async (mainHomeworkId, HomeworkId, accountId, accountMainHomeworkId) => {
        try {
            if (!HomeworkId) {
                setAlertMessage('Cannot auto unlock: homework ID is missing.');
                setShowAlert(true);
                console.error('Auto unlock failed: homeworkId or homework id is undefined.');
                return;
            }
            const response = await submitAutoUnlock({
                main_homework_id: mainHomeworkId,
                homework_id: HomeworkId,
                account_id: accountId,
                account_main_homework_id: accountMainHomeworkId
            });
            fetchData();
        } catch (error) {
            setAlertMessage('Failed to send homework auto unlock request. Please try again.');
            setShowAlert(true);
        }
    };
    const checkRoleParent = role === Role.PARENT ? true : false;
    const handleReset = () => {
        setPage(1);
        setSearch('');
        const param = {search: '', page: 1 }
        fetchData(param);
    };
    return (
        <Box sx={{ px: 10, maxWidth: '100%', pt: 10 }}>
            <Box
                sx={{
                    backgroundColor: '#ffffff',
                    p: 2,
                    borderRadius: 3,
                    mb: 2,
                    display: 'flex',
                    gap: 2
                }}
            >
                <TextField
                    placeholder='Search by title or description'
                    variant='outlined'
                    size='small'
                    fullWidth
                    value={search}
                    onChange={e => {
                        setPage(1), setSearch(e.target.value);
                    }}
                    onKeyDown={e => {
                        if (e.key === 'Enter') {
                            if(loading) return;
                            fetchData();
                        }
                    }}
                />
                <Button
                    variant='contained'
                    className='btn-primary'
                    onClick={() => {
                        if(loading) return;
                        fetchData();
                    }}
                >
                    Search
                </Button>
                <Button
                    variant='contained'
                    sx={{ mr: 2.5, bgcolor: '#8592A3', fontSize: '0.9375rem', textTransform: 'capitalize' }}
                    onClick={handleReset}
                >
                    Reset
                </Button>
            </Box>

            <Box>
                <>
                    <TableContainer
                        component={Paper}
                        elevation={0}
                        sx={{ border: '1px solid', borderColor: 'divider' }}
                    >
                        <Table>
                            <TableHead>
                                <TableRow>
                                    <TableCell sx={{ width: '5%' }}></TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#fff', width: '20%' }}>
                                        Class
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#fff', width: '20%' }}>
                                        Session
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#fff', width: '20%' }}>
                                        Description
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#fff', width: '20%' }}>
                                        {checkRoleParent ? '/Name Student' : ''}
                                    </TableCell>
                                    <TableCell
                                        align='center'
                                        sx={{ fontWeight: 'bold', backgroundColor: '#fff', width: '15%' }}
                                    >
                                        {checkRoleParent ? 'Status' : ''}
                                    </TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {loading ? (
                                    Array.from(new Array(2)).map((_, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{ width: '5%' }}>
                                                <Skeleton variant='circular' width={24} height={24} />
                                            </TableCell>
                                            <TableCell sx={{ width: '30%' }}>
                                                <Skeleton variant='text' width='80%' />
                                            </TableCell>
                                            <TableCell sx={{ width: '20%' }}>
                                                <Skeleton variant='text' width='90%' />
                                            </TableCell>
                                            <TableCell align='center' sx={{ width: '10%' }}>
                                                <Skeleton variant='rectangular' width={40} height={40} />
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : data.length > 0 ? (
                                    data.map((main, index) => {
                                        return (
                                            <React.Fragment key={main.id}>
                                                <TableRow hover sx={{ '&:hover': { bgcolor: 'grey.50' } }}>
                                                    <TableCellCustom>
                                                        <IconButton onClick={() => toggleRow(main.id)}>
                                                            {openRows[main.id] ? (
                                                                <KeyboardArrowUp />
                                                            ) : (
                                                                <KeyboardArrowDown />
                                                            )}
                                                        </IconButton>
                                                    </TableCellCustom>
                                                    <TableCellCustom colSpan={1}>
                                                        <TypographyCustom variant='body1' fontWeight='medium'>
                                                          {main?.get_class?.name}
                                                        </TypographyCustom>
                                                    </TableCellCustom>
                                                    <TableCellCustom colSpan={1}>
                                                        <TypographyCustom variant='body1' fontWeight='medium'>
                                                            {main.session}
                                                        </TypographyCustom>
                                                    </TableCellCustom>
                                                    <TableCellCustom colSpan={1}>
                                                        <TypographyCustom variant='body1' fontWeight='medium'>
                                                            {main.description}
                                                        </TypographyCustom>
                                                    </TableCellCustom>
                                                    <TableCellCustom colSpan={2} align='left'>
                                                        <TypographyCustom variant='body1' fontWeight='medium'>
                                                        </TypographyCustom>
                                                    </TableCellCustom>
                                                </TableRow>

                                                <MainHomeworkRow
                                                    main={main}
                                                    openRows={openRows}
                                                    openHomeworks={openHomeworks}
                                                    toggleHomeworkRow={toggleHomeworkRow}
                                                    role={role}
                                                    handleOpenConfirmDialog={handleOpenConfirmDialog}
                                                    handleOpenConfirmUnlockDialog={handleOpenConfirmUnlockDialog}
                                                    fetchData={fetchData}
                                                    isParent={checkRoleParent}
                                                    accountId={accountId}
                                                    handleAutotUnlock={handleAutotUnlock}
                                                    type={type}
                                                    schedules={schedules}
                                                />
                                            </React.Fragment>
                                        );
                                    })
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={8} align='center'>
                                            No results found.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </>
            </Box>

            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'right', px: '10px', py: '10px' }}>
                {meta?.last_page > 1 && (
                    <Pagination
                        count={meta.last_page}
                        page={page}
                        onChange={(e, value) => setPage(value)}
                        color='primary'
                        shape='rounded'
                        sx={{
                            '& .MuiPaginationItem-root': {
                                borderRadius: 2,
                                minWidth: 36,
                                height: 36
                            }
                        }}
                    />
                )}
            </Box>

            {/* Confirmation Dialog */}
            <ConfirmDialog
                open={openConfirmDialog}
                title={'Confirm Navigation'}
                description={`Do you want to do this ${type == HomeworkType.CLASSWORK? 'classwork': 'homework'}: "${selectedHomeworkTitle}"?`}
                onCancel={handleCloseConfirmDialog}
                onConfirm={handleConfirmStartHomework}
            />
            <ConfirmDialog
                open={openConfirmUnlockDialog}
                title={'Confirm unlock'}
                description={`Are you sure you want to unlock the  ${type == HomeworkType.CLASSWORK? 'classwork': 'homework'}: "${selectedHomeworkTitle}"?`}
                onCancel={handleCloseConfirmUnlockDialog}
                onConfirm={handleRequestUnlock}
            />
            <Dialog
                open={showAlert}
                onClose={() => setShowAlert(false)}
                aria-labelledby='alert-dialog-title'
                aria-describedby='alert-dialog-description'
            >
                <DialogTitle id='alert-dialog-title'>Notification</DialogTitle>
                <DialogContent>
                    <Typography id='alert-dialog-description'>{alertMessage}</Typography>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setShowAlert(false)} autoFocus>
                        Close
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}
