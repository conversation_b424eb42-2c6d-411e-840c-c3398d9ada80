@extends('layouts/contentNavbarLayout')

@section('title', 'Students')

@section('vendor-style')
@endsection
@section('page-style')
  <style>
    th{
      padding: 0.625rem 1rem !important;
    }
  </style>
@endsection
@section('vendor-script')
@endsection

@section('page-script')
  <script>
    $('#class').each(function () {
      $(this).select2({
        theme: 'bootstrap4',
        width: 'style',
        placeholder: $(this).attr('placeholder'),
        allowClear: Boolean($(this).data('allow-clear')),
      });
    });
    function resetForm() {
      $("input[name='name']").val('');
      $("input[name='phone']").val('');
      document.getElementById("level").selectedIndex = 0;
    }
    $('#birthYearPicker input').datepicker({
        format: "yyyy",
        viewMode: "years",
        minViewMode: "years",
        autoclose: true
    });
  </script>
@endsection

@section('content')
  <div class="card" style="height: 100%;min-width: 1200px;overflow-y: auto">
    <div class="d-flex justify-content-sm-between align-items-center">
      <h5 class="card-header">Students</h5>
      <a style="margin-right: 20px;" class="btn btn-primary" href="{{route('admin.students.create')}}">Create Student</a>
    </div>
    <form id="myform">
      <div class="card-body d-flex align-items-center">
        <div class="mb-3" style="padding:  0 10px;">
          <label for="keyword" class="mb-1">Search By Name</label>
          <input name="name" maxlength="100" class="form-control" placeholder="Name" value="{{request()->input('name')}}">
        </div>
        <div class="col-sm-2 mb-3" style="padding: 0 10px">
          <label for="class" class="mb-1">Class</label>
          <select class="form-select" id="class" name="class">
            <option value="all" {{ request()->class == 'all' ? 'selected' : '' }}>All</option>
            @foreach($classes as $class)
              <option value="{{$class->id}}" {{ request()->class == $class->id ? 'selected' : '' }}>{{$class->name}}</option>
            @endforeach
          </select>
        </div>
          {{-- <div class="mb-3" style="padding: 0 10px">
              <label for="status" class="mb-1">Search By</label>
              <input name="email" class="form-control" placeholder="Email" value="{{request()->input('email')}}">
          </div>
        <div class="mb-3" style="padding: 0 10px">
          <label for="status" class="mb-1">Search By</label>
          <input name="phone" class="form-control" placeholder="Phone" value="{{request()->input('phone')}}">
        </div> --}}
        <div class="col-sm-2 mb-3" style="padding: 0 10px">
          <label for="level" class="mb-1">Status</label>
          <select class="form-select" id="level" name="level">
            <option value="all" {{ request()->level == 'all' ? 'selected' : '' }}>All</option>
            <option value="Level Test" {{ request()->level == 'Level Test' ? 'selected' : '' }}>Level Test</option>
            <option value="Waiting" {{ request()->level == 'Waiting' ? 'selected' : '' }}>Waiting</option>
            <option value="Enrolled" {{ request()->level == 'Enrolled' ? 'selected' : '' }}>Enrolled</option>
            <option value="Unenrolled" {{ request()->level == 'Unenrolled' ? 'selected' : '' }}>Unenrolled</option>
          </select>
        </div>
        <div class="col-sm-2 mb-3" style="padding: 0 10px">
          <label for="country" class="mb-1">Country of Residence</label>
          <select class="form-select" id="country" name="country">
            <option value="" {{ request()->country == '' ? 'selected' : '' }}>All</option>
            @foreach($countries as $country)
              <option value="{{ $country['name'] }}"
                {{ request()->country == $country['name'] ? 'selected' : '' }}>
                {{ $country['name'] }}
              </option>
            @endforeach
          </select>
        </div>
        <div class="col-sm-2 mb-3" style="padding: 0 10px">
          <label for="birth_year" class="mb-1">Birth Year</label>
          <div class="input-group" id="birthYearPicker">
            <input type="text" 
                  class="form-control" 
                  id="birth_year" 
                  name="birth_year" 
                  placeholder="Select Year"
                  value="{{ old('birth_year', request()->birth_year) }}"
                  readonly autocomplete="off">
            <span class="input-group-text cursor-pointer iconCalendar">
              <i class='bx bxs-calendar'></i>
            </span>
          </div>
        </div>

        <div style="margin-top: 10px;" style="padding: 0 10px">
          <button type="submit" class="btn btn-primary">Search</button>
          <a href="{{route('admin.students.index')}}" class="cursor-pointer btn btn-secondary" onclick="$('#myform')[0].reset();">Reset</a>
        </div>
      </div>
    </form>
    <div class="text-nowrap">
      <table class="table table-hover">
        <thead>
        <tr>
          <th style="max-width:50px; position: relative; padding-right:20px;  text-align:center;">Action</th>
          <th style="max-width:150px; cursor:pointer; line-height:1.2"
              onclick="sort('name_english','{{request()->name_english == 'desc' ? 'asc' : 'desc'}}')">
              <div style="display:inline-flex; align-items:center; gap:4px; justify-content:center; text-align: center;">
                  <div>
                      Student Name<br>
                      <span style="font-weight:normal; font-size:0.85em;">(English)</span>
                  </div>
                  <div>
                      @include('layouts.sections.sort', ['field' => 'name_english'])
                  </div>
              </div>
          </th>

          <th style="max-width:150px; cursor:pointer; line-height:1.2"
              onclick="sort('name_korea','{{request()->name_korea == 'desc' ? 'asc' : 'desc'}}')">
              <div style="display:inline-flex; align-items:center; gap:4px; justify-content:center; text-align: center;">
                  <div>
                      Student Name<br>
                      <span style="font-weight:normal; font-size:0.85em;">(Korean)</span>
                  </div>
                  <div>
                      @include('layouts.sections.sort', ['field' => 'name_korea'])
                  </div>
              </div>
          </th>


          <th style="max-width:80px; position: relative; padding-right:20px;">Reading Class</th>
          <th style="max-width:80px; position: relative; padding-right:20px;">Writing Class</th>
          <th style="max-width:50px;  position: relative; padding-right:20px;">Birth Year</th>
          <th style="max-width:50px; position: relative; padding-right:20px; white-space: wrap;">Country of Residence</th>

          {{-- Status có sort --}}
          <th style="max-width:50px; cursor:pointer"
              onclick="sort('level_sort','{{request()->level_sort == 'desc' ? 'asc' : 'desc'}}')">
              <div style="display:inline-flex; align-items:center; gap:4px; justify-content:center; text-align: center;">
                  <span>Status</span>
                  <span>
                      @include('layouts.sections.sort', ['field' => 'level_sort'])
                  </span>
              </div>
          </th>

        </tr>
        </thead>
        <tbody class="table-border-bottom-0">
              @forelse($students as $student)
                <tr>
                  <td style="text-align: center; max-width: 50px;">
                    <a style="border: none;
    color: #555555;" href="{{route('admin.students.show',$student->id)}}"><i class='bx bxs-show'></i></a>
                    <a style="border: none;
    color: #555555;" href="{{route('admin.students.edit',$student->id)}}"><i class="bx bx-edit-alt"></i></a>
                    <button style="border: none; padding-left: 0px;background: none;padding-right: 0px;
    color: #555555;" type="button" data-bs-toggle="modal" onclick="showConfirmationModal('{{ $student->id }}')" data-bs-target="#confirmDeleteModal">
                      <i class="bx bx-trash me-1"></i>
                    </button>
                  </td>
                  <td style="max-width: 150px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->name_english}}</td>
                  <td style="max-width: 150px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->name_korea}}</td>
                  <td style="max-width: 80px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ $student->classes->where('subject', 'Reading')->last()->name ?? '' }}</td>
                  <td style="max-width: 80px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{ $student->classes->where('subject', 'Writing')->last()->name ?? '' }}</td>
                  <td style="max-width: 100px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->birthday? \Carbon\Carbon::parse($student->birthday)->format('Y'): "" }}</td>
                  <td style="max-width: 100px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->country_id}}</td>
                  {{-- <td style="max-width: 150px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->phone}}</td> --}}
                  {{-- <td style="max-width: 150px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->parent_phone}}</td> --}}
                  {{-- <td style="max-width: 150px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->created_at}}</td> --}}
                  <td style="max-width: 100px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{{$student->level}}</td>
                </tr>
              @empty
                <tr>
                  <td colspan="9" class="text-center">No data available</td>
                </tr>
              @endforelse
        </tbody>
      </table>
      <div class="demo-inline-spacing d-flex justify-content-center align-items-center" style="padding:  0 20px;">
          @include('layouts.sections.pagination', ['paginator' => $students])
      </div>
    </div>
  </div>
  @include('layouts.sections.modal-delete-confirm', ['route' => route('admin.students.destroy','user_id'),'title' =>'user'])
@endsection
