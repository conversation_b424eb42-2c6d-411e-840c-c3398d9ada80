
<!doctype html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>@yield('title')</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" integrity="sha512-nMNlpuaDPrqlEls3IX/Q56H36qvBASwb3ipuo3MxeWbsQB1881ox0cRv7UPTgBlriqoynt35KjEwgGUeUXIPnw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/core@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fullcalendar/list@4.4.0/main.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
  <link rel="stylesheet" href="{{ mix('/assets/web/css/custom.css') }}">
  <link rel="stylesheet" href="{{ mix('/assets/web/css/output.css') }}">
  <link rel="stylesheet" href="{{ mix('css/app.css') }}">
  <link rel="stylesheet" href="{{asset('assets/vendor/fonts/boxicons.css')}}">
  @stack('after-styles')
  <style>
    .fc-header-toolbar .fc-button {
      background: url("assets/web/images/keyboard_arrow_right.svg") no-repeat center;
    }
  </style>
</head>
<body>

<div>
  @yield('content')
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js" integrity="sha512-2ImtlRlf2VVmiGZsjm9bEyhjGW4dU7B6TNwh/hx/iSByxNENtj3WVE6o/9Lj4TJeVXPi4bnOIMXFIJJAeufa0A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="{{ mix('/assets/web/js/index.js') }}"></script>
<script>
  $('.feedback-report').on('click', function (){
    if ($('.child-menu').hasClass('hidden')) {
      $('aside .logo-full').toggleClass('hidden')
      $('aside .logo-small').toggleClass('hidden block')
      $(this).children('svg').toggleClass('rotate-180')
      $('aside').toggleClass('aside-small')
      $('.box__content--main').toggleClass('aside-small')
      $('aside .menu__text ').toggleClass('hidden')
      $('.header-fixed').toggleClass('aside-small')
      $('.child-menu').toggleClass('hidden')
    }
  })
</script>
@stack('after-scripts')
@php
    $segment1 = request()->segment(1);
    $segment2 = request()->segment(2);
    $doingIds = session('doing_homework_ids', []);
@endphp

@if(($segment1 != 'homeworks' || !$segment2) && auth('web')->user() &&  auth('web')->user()->type == 'student')
    @if(count($doingIds) > 0 && !in_array($segment2, $doingIds))
        <script>
            setInterval(() => {
                toastr.warning("You have some homeworks not submitted. Please submit them before leave.");
            }, 10000);
        </script>
    @endif
@endif
</body>
</html>
